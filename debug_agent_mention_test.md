# 智能体提及前缀调试测试

## 问题描述

用户输入：`@数据分析智能体 工作中心有多少数据`

**预期行为**：
- 前端去除 `@数据分析智能体` 前缀
- 后端只接收到 `工作中心有多少数据`

**实际行为**：
- 后端日志显示：`分析用户消息: 数据分析智能体`
- 说明前缀没有被正确去除

## 调试步骤

### 1. 重启后端服务
```bash
# 停止当前服务
Ctrl+C

# 重新启动
python -m src.server.app
```

### 2. 刷新前端页面
确保获取最新的调试代码

### 3. 发送测试查询
```
@数据分析智能体 工作中心有多少数据
```

### 4. 检查调试日志

#### 前端控制台应该显示：
```
前端智能体解析:
- 原始内容: @数据分析智能体 工作中心有多少数据
- 提及的智能体: 数据分析智能体
- 实际内容: 工作中心有多少数据
- 映射的智能体名称: data_analysis_agent
```

#### 后端日志应该显示：
```
INFO - strip_agent_mention: 输入='工作中心有多少数据' -> 输出='工作中心有多少数据'
INFO - 分析用户消息: 工作中心有多少数据
```

## 可能的问题原因

### 1. 前端解析问题
如果前端控制台显示的 `实际内容` 仍然包含前缀，说明前端解析逻辑有问题。

**可能原因**：
- 空格字符问题（全角/半角）
- 字符串处理逻辑错误
- 智能体名称不匹配

### 2. 后端接收问题
如果前端正确解析，但后端仍收到完整内容，说明数据传输有问题。

**可能原因**：
- API调用时传递了错误的参数
- 消息格式不正确
- 中间件处理问题

### 3. 后端解析问题
如果后端接收到正确内容，但 `strip_agent_mention` 函数没有工作。

**可能原因**：
- 正则表达式匹配问题
- 函数调用逻辑错误
- 消息格式不符合预期

## 修复方案

### 方案1：前端解析问题
如果前端解析有问题，检查：
```typescript
// 检查空格字符
const spaceIndex = content.indexOf(" ");
// 可能需要同时检查全角空格
const spaceIndex = Math.min(
  content.indexOf(" "),  // 半角空格
  content.indexOf("　")   // 全角空格
).filter(i => i >= 0)[0];
```

### 方案2：后端接收问题
检查API调用参数：
```typescript
// 确保发送的是actualContent而不是原始content
const stream = chatStream(
  actualContent ?? "[REPLAY]",  // 这里应该是去除前缀后的内容
  // ...
);
```

### 方案3：后端解析问题
改进 `strip_agent_mention` 函数：
```python
def strip_agent_mention(content: str) -> str:
    if not content:
        return content
    
    # 更严格的匹配模式
    patterns = [
        r'^@数据分析智能体\s+',  # 直接匹配
        r'^@[^\s]+\s+',         # 通用匹配
    ]
    
    for pattern in patterns:
        result = re.sub(pattern, '', content.strip())
        if result != content.strip():
            return result.strip()
    
    return content.strip()
```

## 验证清单

### 前端验证
- [ ] 控制台显示正确的智能体解析日志
- [ ] `实际内容` 不包含 `@数据分析智能体` 前缀
- [ ] `映射的智能体名称` 为 `data_analysis_agent`

### 后端验证
- [ ] `strip_agent_mention` 函数调用日志正确
- [ ] `分析用户消息` 日志不包含智能体前缀
- [ ] 用户查询提取正确

### 功能验证
- [ ] 数据分析智能体正确启动
- [ ] 步骤解析正确显示
- [ ] SQL生成基于正确的用户查询

## 预期结果

修复后，整个流程应该是：

1. **用户输入**：`@数据分析智能体 工作中心有多少数据`
2. **前端处理**：提取 `工作中心有多少数据`，设置智能体为 `data_analysis_agent`
3. **后端接收**：只接收到 `工作中心有多少数据`
4. **智能体处理**：基于 `工作中心有多少数据` 进行分析
5. **关键词提取**：提取 `工作中心 数据` 等关键词
6. **SQL生成**：生成查询工作中心数据的SQL

这样就能确保智能体处理的是用户的真实查询意图，而不是包含智能体名称的完整输入。
