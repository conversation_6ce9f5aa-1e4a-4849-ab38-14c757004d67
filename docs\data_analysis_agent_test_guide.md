# 数据分析智能体测试指南

## 测试前准备

### 1. 启动服务
```bash
# 启动后端服务
cd deer-flow
python -m src.server.app

# 启动前端服务
cd web
npm run dev
```

### 2. 环境变量检查
确保 `.env` 文件中配置了以下变量：
```env
DB_STRUCTURE_SEARCH_URL=http://192.168.30.232:28000/search
SQL_EXEC_URL=http://192.168.30.232:18086/api/sql/execute/v2
NEO4J_URI=bolt://192.168.30.232:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=neo4j123
TOP_K=40
```

## 测试用例

### 1. 基础查询测试
```
@数据分析智能体 查询工作中心数据
```

**预期效果**：
- ✅ 显示5个清晰的步骤
- ✅ 每个步骤有明确的状态指示
- ✅ 能看到AI的推理过程
- ✅ 最终显示SQL和查询结果

### 2. 复杂查询测试
```
@数据分析智能体 统计每个部门的员工数量，按数量降序排列
```

**预期效果**：
- ✅ 关键词提取：部门、员工、统计、排序
- ✅ 数据库结构分析：找到相关表
- ✅ SQL生成：包含GROUP BY和ORDER BY
- ✅ 推理过程：解释为什么选择这些表和字段

### 3. 错误处理测试
```
@数据分析智能体 查询不存在的表
```

**预期效果**：
- ✅ 显示错误状态（红色）
- ✅ 错误信息清晰展示
- ✅ 能定位到具体出错的步骤

## 前端功能测试

### 1. 步骤展示
- [ ] 步骤卡片可以展开/折叠
- [ ] 步骤状态图标正确显示
- [ ] 步骤内容格式化正确

### 2. 推理过程
- [ ] AI推理过程单独显示
- [ ] 推理内容支持Markdown格式
- [ ] 推理过程和结果能区分显示

### 3. 交互功能
- [ ] SQL代码可以复制
- [ ] 表格/原始数据视图可以切换
- [ ] 动画效果流畅

### 4. 状态管理
- [ ] 流式处理时显示"分析中"
- [ ] 有结果时显示"完成"
- [ ] 有错误时显示"错误"
- [ ] 无结果时显示"处理中"

## 后端日志检查

### 1. 步骤标记
检查后端日志中是否包含：
```
[STEP_START] keyword_extraction
[STEP_END] keyword_extraction
[STEP_START] schema_analysis
[STEP_END] schema_analysis
[STEP_START] sql_generation
[REASONING_START]
[REASONING_END]
[STEP_END] sql_generation
[STEP_START] execution
[STEP_END] execution
[STEP_START] result
[STEP_END] result
```

### 2. 推理内容
检查是否包含：
```
### 分析过程：
1. **需求理解**：...
2. **表选择**：...
3. **字段选择**：...
4. **关联分析**：...
5. **条件设置**：...

### 最终SQL：
```sql
SELECT ...
```

### 执行说明：
...
```

## 常见问题排查

### 1. 显示"未知步骤"
**原因**：步骤解析失败
**解决**：检查后端是否正确输出步骤标记

### 2. 状态显示错误
**原因**：状态判断逻辑问题
**解决**：检查是否有SQL或表格数据

### 3. 推理过程不显示
**原因**：推理标记解析失败
**解决**：检查[REASONING_START/END]标记

### 4. 后端报错
**原因**：类型错误或依赖缺失
**解决**：检查relationships类型处理

## 性能测试

### 1. 响应时间
- 关键词提取：< 3秒
- 数据库结构获取：< 5秒
- SQL生成：< 10秒
- SQL执行：< 5秒

### 2. 并发测试
- 多用户同时查询
- 长时间运行稳定性
- 内存使用情况

## 验收标准

### 必须满足
- [x] 每个步骤都能正确显示
- [x] AI推理过程可见
- [x] 状态判断准确
- [x] 错误处理完善
- [x] 交互功能正常

### 加分项
- [x] 动画效果流畅
- [x] 响应速度快
- [x] 界面美观
- [x] 用户体验好

## 测试报告模板

```markdown
## 测试结果

### 基础功能
- [ ] 步骤展示：正常/异常
- [ ] 推理过程：正常/异常  
- [ ] 状态判断：正常/异常
- [ ] 错误处理：正常/异常

### 交互功能
- [ ] 展开折叠：正常/异常
- [ ] 复制功能：正常/异常
- [ ] 视图切换：正常/异常

### 性能表现
- 平均响应时间：___秒
- 内存使用：___MB
- CPU使用率：___%

### 问题记录
1. 问题描述：
   解决方案：
   
2. 问题描述：
   解决方案：

### 总体评价
- 功能完整性：___/10
- 用户体验：___/10
- 性能表现：___/10
- 稳定性：___/10
```

通过这个测试指南，可以全面验证数据分析智能体的改进效果。
