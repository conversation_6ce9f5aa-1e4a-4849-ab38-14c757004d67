// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { ReactRenderer } from "@tiptap/react";
import tippy from "tippy.js";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { getAgents, type Agent } from "~/core/api/agents";

interface AgentSuggestionProps {
  items: Agent[];
  command: (props: { id: string; label: string }) => void;
}

const AgentSuggestionList = forwardRef<
  { onKeyDown: (props: { event: KeyboardEvent }) => boolean },
  AgentSuggestionProps
>((props, ref) => {
  const [selectedIndex, setSelectedIndex] = useState(0);

  const selectItem = (index: number) => {
    const item = props.items[index];
    if (item) {
      props.command({ id: item.name, label: item.display_name });
    }
  };

  const upHandler = () => {
    setSelectedIndex(
      (selectedIndex + props.items.length - 1) % props.items.length,
    );
  };

  const downHandler = () => {
    setSelectedIndex((selectedIndex + 1) % props.items.length);
  };

  const enterHandler = () => {
    selectItem(selectedIndex);
  };

  useEffect(() => setSelectedIndex(0), [props.items]);

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }) => {
      if (event.key === "ArrowUp") {
        upHandler();
        return true;
      }

      if (event.key === "ArrowDown") {
        downHandler();
        return true;
      }

      if (event.key === "Enter") {
        enterHandler();
        return true;
      }

      return false;
    },
  }));

  return (
    <div className="bg-popover border-border max-h-60 overflow-auto rounded-md border p-1 shadow-md">
      {props.items.length ? (
        props.items.map((item, index) => (
          <button
            className={`hover:bg-accent flex w-full items-start gap-2 rounded-sm px-2 py-1 text-left text-sm ${
              index === selectedIndex ? "bg-accent" : ""
            }`}
            key={index}
            onClick={() => selectItem(index)}
          >
            <div className="flex flex-col">
              <div className="font-medium">{item.display_name}</div>
              <div className="text-muted-foreground text-xs">
                {item.description}
              </div>
            </div>
          </button>
        ))
      ) : (
        <div className="text-muted-foreground px-2 py-1 text-sm">
          No agents found
        </div>
      )}
    </div>
  );
});

AgentSuggestionList.displayName = "AgentSuggestionList";

export const agentSuggestion = {
  items: async ({ query }: { query: string }) => {
    const agents = await getAgents();
    return agents.filter((agent) =>
      agent.display_name.toLowerCase().includes(query.toLowerCase()),
    );
  },

  render: () => {
    let component: ReactRenderer<
      { onKeyDown: (props: { event: KeyboardEvent }) => boolean },
      AgentSuggestionProps
    >;
    let popup: any;

    return {
      onStart: (props: any) => {
        component = new ReactRenderer(AgentSuggestionList, {
          props,
          editor: props.editor,
        });

        if (!props.clientRect) {
          return;
        }

        popup = tippy("body", {
          getReferenceClientRect: props.clientRect,
          appendTo: () => document.body,
          content: component.element,
          showOnCreate: true,
          interactive: true,
          trigger: "manual",
          placement: "bottom-start",
        });
      },

      onUpdate(props: any) {
        component.updateProps(props);

        if (!props.clientRect) {
          return;
        }

        popup[0].setProps({
          getReferenceClientRect: props.clientRect,
        });
      },

      onKeyDown(props: any) {
        if (props.event.key === "Escape") {
          popup[0].hide();
          return true;
        }

        return component.ref?.onKeyDown(props) ?? false;
      },

      onExit() {
        popup[0].destroy();
        component.destroy();
      },
    };
  },
}; 