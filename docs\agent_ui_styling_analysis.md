# 智能体UI样式分析文档

## 概述

本文档分析了DeerFlow系统中不同智能体在用户界面中的展示样式和交互方式。系统包含多个智能体角色，每个都有独特的UI表现形式，以优化用户体验并体现其功能特性。

## 消息类型系统

### 消息角色 (MessageRole)

DeerFlow系统定义了三种基本的消息角色：

```typescript
export type MessageRole = "user" | "assistant" | "tool";
```

#### 1. **user** - 用户角色
- **含义**：用户发送的消息
- **UI特征**：右对齐，蓝色背景，体现用户输入

#### 2. **assistant** - 助手角色  
- **含义**：AI智能体的回复消息
- **UI特征**：左对齐，卡片背景，体现AI响应

#### 3. **tool** - 工具角色
- **含义**：工具调用相关的消息
- **UI特征**：通常不直接显示给用户

### 智能体类型 (Agent Types)

系统中定义了多种智能体类型，每种都有特定的功能和UI展示：

```typescript
agent?: "coordinator" | "planner" | "researcher" | "coder" | "reporter" | "podcast" | "default_agent";
```

#### 支持的智能体类型：
- **coordinator** - 协调员
- **planner** - 规划师
- **researcher** - 研究员
- **coder** - 编程员
- **reporter** - 报告员
- **podcast** - 播客生成器
- **default_agent** - 默认智能体

### 消息属性详解

#### 完整的Message接口定义：

```typescript
export interface Message {
  id: string;                           // 消息唯一标识
  threadId: string;                     // 会话线程ID
  agent?: "coordinator" | "planner" | "researcher" | "coder" | "reporter" | "podcast" | "default_agent";
  role: MessageRole;                    // 消息角色：user | assistant | tool
  isStreaming?: boolean;                // 是否为流式输出
  content: string;                      // 消息内容
  contentChunks: string[];              // 内容片段（用于流式输出）
  reasoningContent?: string;            // 推理内容
  reasoningContentChunks?: string[];    // 推理内容片段
  toolCalls?: ToolCallRuntime[];        // 工具调用信息
  options?: Option[];                   // 用户选项（如反馈按钮）
  finishReason?: "stop" | "interrupt" | "tool_calls"; // 结束原因
  interruptFeedback?: string;           // 中断反馈
  resources?: Array<Resource>;          // 相关资源
}
```

#### 消息状态类型：

1. **finishReason 类型**：
   - `"stop"` - 正常结束
   - `"interrupt"` - 被中断
   - `"tool_calls"` - 工具调用结束

2. **isStreaming** - 表示消息是否正在流式输出

3. **contentChunks** - 支持流式输出的内容片段

## 智能体类型与样式分析

### 1. User (用户消息)

**角色定义**：用户输入的消息

**样式特征**：
- **对齐方式**：右对齐显示（`justify-end`）
- **气泡样式**：
  - 背景色：品牌色（`bg-brand`）
  - 圆角：右下角圆角缺失（`rounded-ee-none`）
- **文字样式**：
  - 在深色模式下使用反色样式（`prose-invert`）
  - 颜色自适应：`not-dark:text-secondary dark:text-inherit`

**代码位置**：
```typescript
// web/src/app/chat/components/message-list-view.tsx
message.role === "user" && "justify-end"
message.role === "user" && "bg-brand rounded-ee-none"
```

### 2. Coordinator (协调员智能体)

**角色定义**：负责与用户初始交互，理解问题需求并决定是否移交给其他智能体

**功能职责**：
- 处理问候和简单对话
- 识别和分类用户请求
- 将研究任务移交给规划智能体
- 拒绝不当或有害请求

**样式特征**：
- **展示方式**：标准消息气泡
- **气泡样式**：
  - 背景色：卡片背景（`bg-card`）
  - 圆角：左下角圆角缺失（`rounded-es-none`）
- **对齐方式**：左对齐（默认）

**核心逻辑**：
```python
# src/graph/nodes.py - coordinator_node
def coordinator_node(state: State, config: RunnableConfig):
    """Coordinator node that communicate with customers."""
    # 负责与用户沟通，决定是否移交给planner
```

### 3. Planner (规划智能体)

**角色定义**：制定详细的研究计划并等待用户确认

**功能职责**：
- 分析研究主题
- 制定分步骤的研究计划
- 生成结构化的计划文档
- 等待用户反馈并调整计划

**样式特征**：
- **特殊组件**：使用专门的 `PlanCard` 组件
- **展示区域**：全宽度容器（`w-full px-4`）
- **交互功能**：
  - 支持等待用户反馈（`waitForFeedback`）
  - 处理中断消息（`interruptMessage`）
  - 用户操作回调（`onFeedback`, `onSendMessage`）

**代码实现**：
```typescript
// web/src/app/chat/components/message-list-view.tsx
if (message.agent === "planner") {
  content = (
    <div className="w-full px-4">
      <PlanCard
        message={message}
        waitForFeedback={waitForFeedback}
        interruptMessage={interruptMessage}
        onFeedback={onFeedback}
        onSendMessage={onSendMessage}
      />
    </div>
  );
}
```

### 4. Podcast (播客智能体)

**角色定义**：将研究结果转换为播客音频内容

**功能职责**：
- 基于研究报告生成播客脚本
- 调用语音合成服务生成音频
- 提供音频播放界面
- 处理播客生成错误

**样式特征**：
- **特殊组件**：使用专门的 `PodcastCard` 组件
- **展示区域**：全宽度容器（`w-full px-4`）
- **功能特性**：
  - 音频播放控件
  - 播客标题显示
  - 错误状态处理

**代码实现**：
```typescript
// web/src/app/chat/components/message-list-view.tsx
else if (message.agent === "podcast") {
  content = (
    <div className="w-full px-4">
      <PodcastCard message={message} />
    </div>
  );
}
```

**后端逻辑**：
```python
# src/core/store/store.ts - listenToPodcast
export async function listenToPodcast(researchId: string) {
  // 生成播客音频的完整流程
}
```

### 5. Default Agent (默认智能体)

**角色定义**：简单的问答智能体，直接调用LLM返回结果

**功能职责**：
- 处理简单的问答请求
- 直接调用基础LLM模型
- 提供快速响应
- 无复杂的研究或规划流程

**样式特征**：
- **展示方式**：标准消息气泡（与coordinator相同）
- **气泡样式**：
  - 背景色：卡片背景（`bg-card`）
  - 圆角：左下角圆角缺失（`rounded-es-none`）
- **交互方式**：简单的文字对话

**后端实现**：
```python
# src/server/app.py - _astream_default_agent_generator
async def _astream_default_agent_generator(messages: List[dict], thread_id: str):
    """默认智能体的流式响应生成器，直接调用大模型返回结果"""
    llm = get_llm_by_type("basic")
    # 直接流式调用LLM
```

## MessageBubble 通用样式

所有标准消息（除了特殊卡片组件）都使用 `MessageBubble` 组件：

```typescript
// web/src/app/chat/components/message-list-view.tsx
function MessageBubble({ className, message, children }: {
  className?: string;
  message: Message;
  children: React.ReactNode;
}) {
  return (
    <div
      className={cn(
        `group flex w-fit max-w-[85%] flex-col rounded-2xl px-4 py-3 text-nowrap shadow`,
        message.role === "user" && "bg-brand rounded-ee-none",
        message.role === "assistant" && "bg-card rounded-es-none",
        className,
      )}
    >
      {children}
    </div>
  );
}
```

## 样式设计原则

### 1. 角色区分
- **用户消息**：右对齐，蓝色背景，突出用户输入
- **智能体消息**：左对齐，卡片背景，体现AI回应

### 2. 功能区分
- **简单对话**：标准气泡样式（coordinator, default_agent）
- **复杂功能**：专用卡片组件（planner, podcast）

### 3. 交互优化
- **planner**：提供交互按钮，支持计划编辑和确认
- **podcast**：集成播放控件，优化音频体验

### 4. 视觉层次
- 全宽度容器用于重要功能（规划、播客）
- 受限宽度气泡用于常规对话
- 圆角设计增强对话流畅感

## 代码架构

### 主要文件结构
```
web/src/app/chat/components/
├── message-list-view.tsx      # 主要的消息展示逻辑
├── plan-card.tsx             # 规划智能体专用组件
└── podcast-card.tsx          # 播客智能体专用组件

src/graph/nodes.py            # 后端智能体节点实现
src/core/store/store.ts       # 前端状态管理
```

### 消息类型定义
```typescript
// web/src/core/messages/types.ts
export interface Message {
  agent?: "coordinator" | "planner" | "researcher" | "coder" | "reporter" | "podcast" | "default_agent";
  role: MessageRole;
  // ... 其他属性
}
```

## 扩展指南

### 添加新智能体类型

1. **类型定义**：在 `types.ts` 中添加新的 agent 类型
2. **样式判断**：在 `message-list-view.tsx` 中添加条件判断
3. **专用组件**：如需特殊样式，创建专用Card组件
4. **后端实现**：在 `nodes.py` 中实现相应的处理逻辑

### 样式自定义

1. **修改现有样式**：调整 `MessageBubble` 或专用组件的className
2. **添加新样式**：在 `prosemirror.css` 中定义新的CSS类
3. **主题适配**：确保支持暗色模式和亮色模式

## 总结

DeerFlow的智能体UI设计体现了以下核心思想：

1. **功能导向**：不同智能体的界面设计紧密配合其功能特性
2. **用户体验**：通过视觉区分和交互优化提升用户体验
3. **系统一致性**：在满足个性化需求的同时保持整体设计统一
4. **可扩展性**：模块化的组件设计便于添加新的智能体类型

这种设计模式为多智能体系统的UI设计提供了很好的参考案例。

## 附录：其他智能体类型

### 研究流程中的智能体

除了在UI中有专门展示的智能体外，系统还包含以下在后台工作的智能体：

#### 1. **Researcher (研究员智能体)**
- **功能**：负责信息搜索和数据收集
- **UI展示**：在研究活动块中显示，使用 `ActivityMessage` 组件
- **特点**：不使用专门的卡片，而是嵌入到研究流程中

#### 2. **Coder (编程智能体)**  
- **功能**：负责代码生成和数据处理
- **UI展示**：同样在研究活动中显示
- **特点**：处理需要编程解决的研究步骤

#### 3. **Reporter (报告智能体)**
- **功能**：生成最终研究报告
- **UI展示**：报告内容直接显示为最终结果
- **特点**：负责整合所有研究发现

### 智能体层次结构

```
用户输入
    ↓
Coordinator (协调员) - 初始处理和分流
    ↓
Planner (规划员) - 制定研究计划
    ↓
研究团队执行:
    ├── Researcher (研究员) - 信息搜索
    ├── Coder (编程员) - 数据处理  
    └── Reporter (报告员) - 报告生成
    ↓
Podcast (播客) - 可选的音频转换
```

### 消息处理逻辑

在 `store.ts` 中，不同类型的智能体消息有不同的处理方式：

```typescript
// 研究相关的智能体会触发研究流程
if (
  message.agent === "coder" ||
  message.agent === "reporter" ||
  message.agent === "researcher"
) {
  if (!getOngoingResearchId()) {
    const id = message.id;
    appendResearch(id);
    openResearch(id);
  }
  appendResearchActivity(message);
}
```

### 智能体选择机制

系统支持通过 `@` 语法选择特定智能体：

```typescript
// 智能体提及检测
if (content && content.startsWith("@")) {
  const spaceIndex = content.indexOf(" ");
  if (spaceIndex > 0) {
    const mentionedAgent = content.substring(1, spaceIndex);
    actualContent = content.substring(spaceIndex + 1).trim();
    
    // 智能体名称映射
    if (mentionedAgent === "默认智能体") {
      agentName = "default_agent";
    } else if (mentionedAgent === "报告智能体") {
      agentName = "reporter_agent";
    }
  }
}
```

### 支持的智能体命令
- `@默认智能体` → `default_agent`
- `@报告智能体` → `reporter_agent`

这种设计模式为多智能体系统的UI设计提供了很好的参考案例。 