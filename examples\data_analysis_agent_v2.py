"""
数据分析智能体 - 纯函数式设计
采用意图识别→直接执行对应逻辑的模式，实现真正的流式交互
生成SQL后直接执行，无需用户确认
"""

import uuid
import os
import json
import re
import logging
import aiohttp
import httpx
import sqlparse
import time
from typing import Dict, List, Optional, AsyncGenerator
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
from neo4j import GraphDatabase

from ag_ui.encoder import EventEncoder
from ag_ui.core import (
    EventType,
    RunStartedEvent,
    RunFinishedEvent,
    TextMessageStartEvent,
    TextMessageContentEvent,
    TextMessageEndEvent
)
from agents.utils import strip_agent_mention
from llm.deepseek_llm import DeepSeekLLM
from llm.qwen_llm import QwenLLM
from llm.closeai_llm import CloseAILLM

class TaskCancelledException(Exception):
    """任务取消异常"""
    pass

# 自动同步DEEPSEEK_API_KEY到OPENAI_API_KEY，兼容langchain_openai
if not os.getenv("OPENAI_API_KEY") and os.getenv("DEEPSEEK_API_KEY"):
    os.environ["OPENAI_API_KEY"] = os.getenv("DEEPSEEK_API_KEY")

# 全局取消任务字典
cancelled_tasks = {}

def cancel_task(thread_id, run_id):
    """取消指定的任务"""
    cancelled_tasks[(thread_id, run_id)] = True
    print(f"[DEBUG] 任务已标记为取消: thread_id={thread_id}, run_id={run_id}")

def clear_cancelled_task(thread_id, run_id):
    """清理已取消的任务标记"""
    cancelled_tasks.pop((thread_id, run_id), None)

# 全局智能体实例（用于缓存管理）
_agent_instance = None

def get_agent_instance():
    global _agent_instance
    if _agent_instance is None:
        _agent_instance = DataAnalysisAgentV2()
    return _agent_instance


class DataAnalysisAgentV2:
    """数据分析智能体 - 纯函数式设计"""
    
    # SQL生成相关的固定提示词
    SQL_GENERATION_RULES = """
## SQL生成规范
- **严格表关联规则**：JOIN条件必须在"表之间关系"中有明确定义，禁止创造不存在的关联关系
- **禁止随意关联**：如果两个表在"表之间关系"中没有直接关系，不要强行JOIN，考虑通过中间表关联
- **ID字段优先**：优先使用ID字段进行关联，避免使用名称字段进行字符串匹配
- **字段名称检查**：仔细检查字段名称，确保使用正确的字段进行关联
- 仅生成SELECT语句，禁止包含INSERT/UPDATE/DELETE等DML操作
- 严格嵌入提供的DDL元数据，禁止使用任何未声明的表或字段
- 表名带KMMOM schema
- 过滤删除数据
- 根据用户的语义及表结构，尽量对字段进行中文别名处理
- 严格检查使用的表字段，不要引用非涉及表的字段
"""

    SQL_OUTPUT_FORMAT = """
## 输出格式要求
- 只输出SQL语句，不要包含任何其他内容
- 不要输出"SQL:"、"生成的SQL:"等前缀
- 不要输出任何解释、注释或说明
- 不要输出markdown格式
- 不要输出代码块标记
- 直接输出纯SQL语句
"""

    def __init__(self):
        """初始化智能体"""
        # 初始化所有LLM实例，按重试顺序排列
        self.llm_instances = {
            'deepseek': DeepSeekLLM(),
            'qwen': QwenLLM(),
            'closeai': CloseAILLM()
        }
        # 默认使用DeepSeek
        self.llm = self.llm_instances['deepseek']
        
        # LLM重试顺序：DeepSeek → Qwen → CloseAI
        self.llm_retry_order = ['deepseek', 'qwen', 'closeai']
        
        # 从环境变量读取top_k值，默认为40
        self.default_top_k = int(os.getenv("TOP_K", "40"))
        
        # 缓存表结构和关系信息，按thread_id存储
        self.db_structure_cache = {}  # {thread_id: db_structure}
        self.relationships_cache = {}  # {thread_id: relationships}
    
    def _get_cached_db_info(self, thread_id: str) -> tuple[str, list]:
        """获取缓存的数据库信息"""
        db_structure = self.db_structure_cache.get(thread_id, "")
        relationships = self.relationships_cache.get(thread_id, [])
        return db_structure, relationships
    
    def _cache_db_info(self, thread_id: str, db_structure: str, relationships: list):
        """缓存数据库信息"""
        self.db_structure_cache[thread_id] = db_structure
        self.relationships_cache[thread_id] = relationships
    
    def _clear_cache(self, thread_id: str):
        """清理指定线程的缓存"""
        self.db_structure_cache.pop(thread_id, None)
        self.relationships_cache.pop(thread_id, None)
    
    async def _recognize_intent(self, messages: List) -> str:
        """意图识别 - 简化版本，不再区分continue意图"""
        print(f"[DEBUG] 开始意图识别，消息数: {len(messages)}")
        
        if not messages:
            return "initial"
        
        # 获取最后一条用户消息
        last_message = messages[-1]
        if not hasattr(last_message, 'role') or last_message.role != "user":
            return "initial"
        
        content = strip_agent_mention(last_message.content)
        print(f"[DEBUG] 分析用户消息: {content}")
        
        # 使用大模型进行意图识别
        return await self._llm_intent_recognition(messages)
    
    async def _llm_intent_recognition(self, messages: List) -> str:
        """使用大模型进行意图识别 - 简化版本"""
        # 构建对话历史
        conversation_history = ""
        has_sql_generated = False
        last_user_message = ""
        
        for msg in messages:
            if hasattr(msg, 'role'):
                if msg.role == "user":
                    user_content = strip_agent_mention(msg.content)
                    conversation_history += f"用户: {user_content}\n"
                    last_user_message = user_content  # 记录最后一条用户消息
                elif msg.role == "assistant":
                    content_preview = msg.content[:200] + "..." if len(msg.content) > 200 else msg.content
                    conversation_history += f"助手: {content_preview}\n"
                    # 检查是否已经生成过SQL
                    if "SQL生成完毕" in msg.content or "```sql" in msg.content:
                        has_sql_generated = True
        
        # 构建意图识别提示
        intent_prompt = f"""你是一个数据分析意图识别助手。请分析用户的最新消息，判断用户的意图。

对话历史：
{conversation_history}

用户最新消息：{last_user_message}

请判断用户的最新消息属于以下哪种意图：
1. "initial" - 用户想要进行新的数据分析（首次查询，包含分析需求）
2. "new_query" - 用户想要进行全新的数据分析（明确提到新的查询需求，与之前的分析无关）
3. "modify_sql" - 用户想要修改之前生成的SQL（对当前SQL提出修改意见或优化建议）

判断规则：
- 如果对话中还没有生成过SQL，且用户提出分析需求，选择 "initial"
- 如果已经生成过SQL，但用户提出了**完全不同的数据分析需求**（如从"创建者查询"变为"明细数据查询"），选择 "new_query"
- 如果已经生成过SQL，用户明确提到"新的查询"、"重新分析"、"换个查询"等，选择 "new_query"
- 如果已经生成过SQL，用户提出修改意见（如"改一下"、"优化"、"加个条件"等），选择 "modify_sql"

**重要判断标准**：
- 如果用户的最新消息包含具体的数据分析需求（如"查询XX数据"、"统计XX"、"分析XX"等），即使之前生成过SQL，也应该选择 "new_query"

当前是否已生成SQL: {"是" if has_sql_generated else "否"}

请只回复意图类型，不要包含其他内容：initial、new_query 或 modify_sql"""

        try:
            # 调用大模型
            messages_for_llm = [{"role": "user", "content": intent_prompt}]
            
            # 直接调用默认LLM
            intent_result = await self.llm.ainvoke(messages_for_llm)
            intent_result = intent_result.strip().lower()
            
            # 验证返回的意图
            if intent_result in ["initial", "new_query", "modify_sql"]:
                print(f"[DEBUG] 大模型识别意图: {intent_result}")
                return intent_result
            else:
                print(f"[DEBUG] 大模型返回了无效的意图: {intent_result}，默认使用 initial")
                return "initial"
                
        except Exception as e:
            print(f"[DEBUG] 大模型意图识别失败: {e}，默认使用 initial")
            return "initial"
    
    def _extract_user_query(self, messages: List) -> str:
        """提取用户查询"""
        for msg in reversed(messages):
            if hasattr(msg, 'role') and msg.role == "user":
                content = strip_agent_mention(msg.content)
                return content.strip()
        return ""
    
    def _extract_modification_request(self, messages: List) -> str:
        """提取修改请求"""
        for msg in reversed(messages):
            if hasattr(msg, 'role') and msg.role == "user":
                content = strip_agent_mention(msg.content)
                return content.strip()
        return ""
    
    def _extract_previous_sql(self, messages: List) -> str:
        """从对话历史中提取之前生成的SQL"""
        for msg in reversed(messages):
            if hasattr(msg, 'role') and msg.role == "assistant":
                content = msg.content
                # 查找SQL代码块
                sql_match = re.search(r'```sql\n(.*?)\n```', content, re.DOTALL)
                if sql_match:
                    return sql_match.group(1).strip()
        return ""
    
    async def _extract_keywords(self, user_input: str, thread_id: str = "", run_id: str = "") -> str:
        """提取关键词"""
        if thread_id and run_id:
            self._check_cancelled_and_raise(thread_id, run_id)
            
        prompt = f"""请从以下查询中提取关键词用于数据库搜索：

查询内容：{user_input}

提取要求：
1. 优先提取业务实体（如：工作中心、生产订单、物料、人员等）
2. 提取业务属性（如：创建者、负责人、状态、类型等）
3. 忽略具体的人名、日期、数值等实例数据
4. 输出格式：关键词之间用空格分隔
5. 按重要性排序：业务实体 > 业务属性 > 其他相关词汇
6. 只返回关键词，不要其他解释

示例：
输入：创建者为张三的工作中心
输出：工作中心 创建者

输入：查询生产订单的物料信息和状态
输出：生产订单 物料 状态 信息

输入：统计各部门人员的数量
输出：部门 人员 数量 统计

请提取关键词："""
        
        messages = [
            {"role": "system", "content": "你是一个专业的数据库查询关键词提取助手，专注于提取业务实体和属性。"},
            {"role": "user", "content": prompt}
        ]
        return await self.llm.ainvoke(messages)
    
    async def _fetch_db_structure(self, keywords: str, thread_id: str = "", run_id: str = "", top_k: int = None) -> str:
        """获取数据库结构"""
        if thread_id and run_id:
            self._check_cancelled_and_raise(thread_id, run_id)
        
        # 如果没有指定top_k，使用默认值
        if top_k is None:
            top_k = self.default_top_k
            
        async with aiohttp.ClientSession() as session:
            url = f"http://**************:28000/search?query={keywords}&top_k={top_k}"
            async with session.get(url) as resp:
                if resp.status == 200:
                    return await resp.text()
                else:
                    raise Exception(f"接口请求失败: {resp.status}")
    
    async def _fetch_relationships(self, keywords: str, thread_id: str = "", run_id: str = "") -> list:
        """获取表关系"""
        if thread_id and run_id:
            self._check_cancelled_and_raise(thread_id, run_id)
            
        NEO4J_URI = os.getenv("NEO4J_URI", "bolt://**************:7687")
        NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
        NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "neo4j123")
        
        from neo4j import GraphDatabase
        driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
        try:
            with driver.session() as session:
                query = """
                MATCH (a:Table)-[r:REFERENCES]->(b:Table)
                RETURN a.name AS source_table, r.from AS source_field,
                       b.name AS target_table, r.to AS target_field
                """
                result = session.run(query)
                relationships = []
                for record in result:
                    rel = f"{record['source_table']}({record['source_field']}) references {record['target_table']}({record['target_field']})"
                    relationships.append(rel)
            return relationships
        finally:
            driver.close()
    
    def _build_sql_generation_prompt(self, user_query: str, modification_request: str = "", 
                                   db_structure: str = "", relationships: List[str] = None,
                                   previous_sql: str = "", is_retry: bool = False) -> str:
        """构建SQL生成提示词"""
        prompt = f"""
你是一名专业的达梦SQL生成助手。请根据以下信息生成SQL：

用户需求: {user_query}
"""
        if modification_request:
            prompt += f"\n用户修改意见: {modification_request}"
        # 只有在非重试且有previous_sql时才加入上次SQL
        if previous_sql and not is_retry:
            prompt += f"\n上次SQL:\n{previous_sql}"
        
        prompt += f"""

表结构格式: <表名>[表注释](<字段名1>:<类型>:<注释>:[枚举值1:注释1,枚举值2:注释2,...], ...)
表之间关系格式：表A元数据(字段名) REFERENCES 表B元数据(字段名)

表结构:
{db_structure}

表之间关系:
{relationships if relationships else '无'}

{self.SQL_GENERATION_RULES}

{self.SQL_OUTPUT_FORMAT}

请根据上述信息生成SQL。
"""
        
        return prompt
    
    async def _execute_sql(self, sql: str) -> Dict:
        """执行SQL"""
        async with httpx.AsyncClient(timeout=30) as client:
            url = os.getenv("SQL_EXEC_URL", "http://**************:18086/api/sql/execute/v2")
            response = await client.post(url, json={"sql": sql, "limit": 20})
            response.raise_for_status()
            return response.json()
    
    def _generate_table_json(self, data: List[Dict]) -> Dict:
        """生成结构化表格JSON"""
        if not data or len(data) == 0:
            return {
                "type": "table",
                "data": [],
                "total": 0
            }
        return {
            "type": "table",
            "data": data, 
            "total": len(data)
        }
    
    def _is_cancelled(self, thread_id: str, run_id: str) -> bool:
        """检查任务是否被取消"""
        is_cancelled = cancelled_tasks.get((thread_id, run_id), False)
        if is_cancelled:
            print(f"[DEBUG] 检测到任务已取消: thread_id={thread_id}, run_id={run_id}")
        return is_cancelled
    
    def _check_cancelled_and_raise(self, thread_id: str, run_id: str):
        """检查取消状态，如果已取消则抛出异常"""
        if self._is_cancelled(thread_id, run_id):
            raise TaskCancelledException("任务已取消")
    
    async def _handle_sql_generation_and_execution(self, messages: List, intent: str, encoder: EventEncoder, 
                                                 message_id: str, thread_id: str, run_id: str) -> AsyncGenerator[str, None]:
        """处理SQL生成和执行逻辑 - 合并原有的生成和执行逻辑"""
        user_query = self._extract_user_query(messages)
        
        try:
            if intent == "initial":
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=message_id,
                    delta=f"[日志] 开始分析查询：「{user_query}」\n"
                ))
            elif intent == "new_query":
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=message_id,
                    delta=f"[日志] 开始新的数据分析：「{user_query}」\n"
                ))
            elif intent == "modify_sql":
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=message_id,
                    delta="[日志] 收到SQL修改请求，正在重新生成并执行...\n"
                ))
            
            # 检查取消状态
            if self._is_cancelled(thread_id, run_id):
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=message_id,
                    delta="[日志] 任务已取消\n"
                ))
                return
            
            # 如果需要获取基础数据
            if intent in ["initial", "new_query"]:
                # new_query时清理旧缓存，重新获取数据
                if intent == "new_query":
                    self._clear_cache(thread_id)
                
                # 1. 关键词提取
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=message_id,
                    delta="[日志] 正在提取关键词...\n"
                ))
                
                keywords = await self._extract_keywords(user_query, thread_id, run_id)
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=message_id,
                    delta=f"[日志] 关键词提取完毕: {keywords}\n"
                ))
                
                if self._is_cancelled(thread_id, run_id):
                    return
                
                # 2. 获取数据库结构
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=message_id,
                    delta="[日志] 正在获取数据库结构...\n"
                ))
                
                db_structure, relationships = self._get_cached_db_info(thread_id)
                if not db_structure or not relationships:
                    db_structure = await self._fetch_db_structure(keywords, thread_id, run_id)
                    yield encoder.encode(TextMessageContentEvent(
                        type=EventType.TEXT_MESSAGE_CONTENT,
                        message_id=message_id,
                        delta="[日志] 数据库结构获取完毕\n"
                    ))
                    
                    if self._is_cancelled(thread_id, run_id):
                        return
                    
                    # 3. 获取表关系
                    yield encoder.encode(TextMessageContentEvent(
                        type=EventType.TEXT_MESSAGE_CONTENT,
                        message_id=message_id,
                        delta="[日志] 正在获取表关系...\n"
                    ))
                    
                    relationships = await self._fetch_relationships(keywords, thread_id, run_id)
                    yield encoder.encode(TextMessageContentEvent(
                        type=EventType.TEXT_MESSAGE_CONTENT,
                        message_id=message_id,
                        delta="[日志] 表关系获取完毕\n"
                    ))
                    
                    # 缓存获取的信息
                    self._cache_db_info(thread_id, db_structure, relationships)
                else:
                    yield encoder.encode(TextMessageContentEvent(
                        type=EventType.TEXT_MESSAGE_CONTENT,
                        message_id=message_id,
                        delta="[日志] 使用缓存的数据库结构和表关系\n"
                    ))
            else:
                # 对于修改请求，使用缓存的结构信息
                db_structure, relationships = self._get_cached_db_info(thread_id)
            
            if self._is_cancelled(thread_id, run_id):
                return
            
            # 4. 生成SQL
            yield encoder.encode(TextMessageContentEvent(
                type=EventType.TEXT_MESSAGE_CONTENT,
                message_id=message_id,
                delta="[日志] 正在生成SQL...\n"
            ))
            
            # 构建提示词
            modification_request = self._extract_modification_request(messages) if intent == "modify_sql" else ""
            previous_sql = self._extract_previous_sql(messages) if intent == "modify_sql" else ""
            
            prompt = self._build_sql_generation_prompt(
                user_query=user_query,
                modification_request=modification_request,
                db_structure=db_structure,
                relationships=relationships,
                previous_sql=previous_sql
            )
            
            # 生成SQL - 初始生成只使用DeepSeek
            system_prompt = self._generate_dynamic_system_prompt(0)
            
            sql = ""
            async for chunk in self.llm.astream([
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ]):
                self._check_cancelled_and_raise(thread_id, run_id)
                sql += chunk
            
            sql = sql.strip()
            formatted_sql = sqlparse.format(sql, reindent=True, keyword_case='upper')
            print(f"[DEBUG] 生成SQL=\n{sql}")
            
            yield encoder.encode(TextMessageContentEvent(
                type=EventType.TEXT_MESSAGE_CONTENT,
                message_id=message_id,
                delta="[日志] SQL生成完毕\n\n**生成的SQL:**\n"
            ))
            
            yield encoder.encode(TextMessageContentEvent(
                type=EventType.TEXT_MESSAGE_CONTENT,
                message_id=message_id,
                delta=f"```sql\n{formatted_sql}\n```\n\n"
            ))
            
            # 5. 直接执行SQL
            yield encoder.encode(TextMessageContentEvent(
                type=EventType.TEXT_MESSAGE_CONTENT,
                message_id=message_id,
                delta="[日志] 正在执行SQL...\n"
            ))
            
            if self._is_cancelled(thread_id, run_id):
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=message_id,
                    delta="[日志] 任务已取消\n"
                ))
                return
            
            try:
                # 执行SQL
                result = await self._execute_sql(sql)
                
                # 检查结果
                success = True
                error_msg = ""
                is_execution_error = False  # 区分是执行错误还是结果为0
                
                if isinstance(result, dict):
                    if ('code' in result and result['code'] != 0):
                        # SQL执行报错
                        success = False
                        error_msg = result.get('msg', '执行错误')
                        is_execution_error = True
                    elif ('total' in result and result['total'] == 0):
                        # 执行成功但结果为0
                        success = False
                        error_msg = "查询结果总数为0，可能需要检查查询条件或表关系"
                        is_execution_error = False
                
                if success:
                    yield encoder.encode(TextMessageContentEvent(
                        type=EventType.TEXT_MESSAGE_CONTENT,
                        message_id=message_id,
                        delta="[日志] SQL执行成功\n"
                    ))
                    
                    # 输出结果
                    if isinstance(result, dict) and "data" in result:
                        data = result["data"]
                        total = result.get("total", len(data) if isinstance(data, list) else 0)
                        
                        yield encoder.encode(TextMessageContentEvent(
                            type=EventType.TEXT_MESSAGE_CONTENT,
                            message_id=message_id,
                            delta=f"**查询结果** (共{total}条记录):\n\n"
                        ))
                        
                        # 生成表格JSON
                        table_json = self._generate_table_json(data if isinstance(data, list) else [])
                        yield encoder.encode(TextMessageContentEvent(
                            type=EventType.TEXT_MESSAGE_CONTENT,
                            message_id=message_id,
                            delta=f"[TABLE_DATA] {json.dumps(table_json, ensure_ascii=False)}\n"
                        ))
                else:
                    # 尝试自动修正SQL
                    yield encoder.encode(TextMessageContentEvent(
                        type=EventType.TEXT_MESSAGE_CONTENT,
                        message_id=message_id,
                        delta=f"[日志] SQL执行失败: {error_msg}，尝试自动修正...\n"
                    ))
                    
                    # 自动修正逻辑，传递错误类型
                    async for chunk in self._handle_auto_sql_fix(messages, sql, error_msg, is_execution_error, encoder, message_id, thread_id, run_id):
                        yield chunk
                        
            except Exception as e:
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=message_id,
                    delta=f"[错误] SQL执行失败: {str(e)}\n"
                ))
            
        except Exception as e:
            yield encoder.encode(TextMessageContentEvent(
                type=EventType.TEXT_MESSAGE_CONTENT,
                message_id=message_id,
                delta=f"[错误] SQL生成执行过程中发生错误: {str(e)}\n"
            ))
    
    async def _handle_auto_sql_fix(self, messages: List, failed_sql: str, error_msg: str,
                                 is_execution_error: bool, encoder: EventEncoder, message_id: str, thread_id: str, run_id: str,
                                 max_attempts: int = 3) -> AsyncGenerator[str, None]:
        """处理SQL自动修正 - 基于SQL执行结果的模型切换"""
        user_query = self._extract_user_query(messages)
        
        # 按照LLM重试顺序尝试不同的模型
        for llm_index, llm_name in enumerate(self.llm_retry_order):
            if self._is_cancelled(thread_id, run_id):
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=message_id,
                    delta="[日志] 任务已取消\n"
                ))
                return
            
            # 跳过第一个模型（DeepSeek），因为它已经失败了
            if llm_index == 0:
                continue
            
            # 计算当前尝试的top_k值：基于环境变量的default_top_k递增
            current_top_k = self.default_top_k + llm_index * 20
            
            yield encoder.encode(TextMessageContentEvent(
                type=EventType.TEXT_MESSAGE_CONTENT,
                message_id=message_id,
                delta=f"[日志] 尝试使用 {llm_name} 重新生成SQL，扩大搜索范围(top_k={current_top_k})...\n"
            ))
            
            # 重新获取数据库结构信息，使用更大的top_k值
            keywords = await self._extract_keywords(user_query, thread_id, run_id)
            yield encoder.encode(TextMessageContentEvent(
                type=EventType.TEXT_MESSAGE_CONTENT,
                message_id=message_id,
                delta=f"[日志] 关键词: {keywords}\n"
            ))
            
            yield encoder.encode(TextMessageContentEvent(
                type=EventType.TEXT_MESSAGE_CONTENT,
                message_id=message_id,
                delta="[日志] 重新获取数据库结构和表关系...\n"
            ))
            
            db_structure = await self._fetch_db_structure(keywords, thread_id, run_id, top_k=current_top_k)
            relationships = await self._fetch_relationships(keywords, thread_id, run_id)
            
            # 更新缓存
            self._cache_db_info(thread_id, db_structure, relationships)
            
            yield encoder.encode(TextMessageContentEvent(
                type=EventType.TEXT_MESSAGE_CONTENT,
                message_id=message_id,
                delta="[日志] 数据库信息更新完毕\n"
            ))
            
            # 构建修正提示词
            if is_execution_error:
                fail_reason = f"之前使用其他模型生成的SQL执行失败：{error_msg}，请重新生成正确的SQL。"
                prompt = self._build_sql_generation_prompt(
                    user_query=user_query,
                    modification_request=fail_reason,
                    db_structure=db_structure,
                    relationships=relationships,
                    previous_sql=failed_sql,
                    is_retry=False
                )
            else:
                fail_reason = f"""之前使用其他模型生成的SQL查询结果为0条记录，说明SQL中的表关联或查询条件有问题。

问题SQL：
{failed_sql}

现在已经扩大了数据库表结构范围(top_k={current_top_k})，请重新分析用户需求并生成新的SQL。

**重要提醒**：
1. 严格按照提供的表关系进行JOIN，不要创造不存在的关联关系
2. 如果表之间没有直接关系，不要强行关联，考虑通过中间表或其他方式
3. 仔细检查字段名称，确保使用正确的字段进行关联
4. 优先使用ID字段进行关联，避免使用名称字段进行字符串匹配"""
                
                prompt = self._build_sql_generation_prompt(
                    user_query=user_query,
                    modification_request=fail_reason,
                    db_structure=db_structure,
                    relationships=relationships,
                    previous_sql="",
                    is_retry=True
                )
            
            # 使用当前LLM生成SQL
            current_llm = self.llm_instances[llm_name]
            system_prompt = self._generate_dynamic_system_prompt(llm_index)
            
            print(f"[DEBUG] 使用 {llm_name} 重新生成SQL，top_k={current_top_k}")
            
            try:
                new_sql = ""
                async for chunk in current_llm.astream([
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ]):
                    if self._is_cancelled(thread_id, run_id):
                        return
                    new_sql += chunk
                
                new_sql = new_sql.strip()
                formatted_sql = sqlparse.format(new_sql, reindent=True, keyword_case='upper')
                print(f"[DEBUG] {llm_name} 生成SQL=\n{new_sql}")
                
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=message_id,
                    delta=f"[日志] {llm_name} 生成的SQL:\n```sql\n{formatted_sql}\n```\n"
                ))
                
                # 立即执行新生成的SQL
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=message_id,
                    delta=f"[日志] 执行 {llm_name} 生成的SQL...\n"
                ))
                
                result = await self._execute_sql(new_sql)
                
                # 检查结果
                success = True
                new_error_msg = ""
                new_is_execution_error = False
                
                if isinstance(result, dict):
                    if ('code' in result and result['code'] != 0):
                        # SQL执行报错
                        success = False
                        new_error_msg = result.get('msg', '执行错误')
                        new_is_execution_error = True
                    elif ('total' in result and result['total'] == 0):
                        # 执行成功但结果为0
                        success = False
                        new_error_msg = "查询结果总数为0，可能需要检查查询条件或表关系"
                        new_is_execution_error = False
                
                if success:
                    yield encoder.encode(TextMessageContentEvent(
                        type=EventType.TEXT_MESSAGE_CONTENT,
                        message_id=message_id,
                        delta=f"[日志] {llm_name} 生成的SQL执行成功！\n"
                    ))
                    
                    # 输出结果
                    if isinstance(result, dict) and "data" in result:
                        data = result["data"]
                        total = result.get("total", len(data) if isinstance(data, list) else 0)
                        
                        yield encoder.encode(TextMessageContentEvent(
                            type=EventType.TEXT_MESSAGE_CONTENT,
                            message_id=message_id,
                            delta=f"**查询结果** (共{total}条记录):\n\n"
                        ))
                        
                        # 生成表格JSON
                        table_json = self._generate_table_json(data if isinstance(data, list) else [])
                        yield encoder.encode(TextMessageContentEvent(
                            type=EventType.TEXT_MESSAGE_CONTENT,
                            message_id=message_id,
                            delta=f"[TABLE_DATA] {json.dumps(table_json, ensure_ascii=False)}\n"
                        ))
                    return  # 成功，退出重试循环
                else:
                    # 当前模型生成的SQL也失败了，继续尝试下一个模型
                    yield encoder.encode(TextMessageContentEvent(
                        type=EventType.TEXT_MESSAGE_CONTENT,
                        message_id=message_id,
                        delta=f"[日志] {llm_name} 生成的SQL执行失败: {new_error_msg}\n"
                    ))
                    
                    # 更新失败信息，继续下一个模型
                    failed_sql = new_sql
                    error_msg = new_error_msg
                    is_execution_error = new_is_execution_error
                    
            except Exception as e:
                # 当前LLM调用失败，继续尝试下一个
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=message_id,
                    delta=f"[日志] {llm_name} 调用失败: {str(e)}，尝试下一个模型...\n"
                ))
                continue
        
        # 所有模型都失败了
        yield encoder.encode(TextMessageContentEvent(
            type=EventType.TEXT_MESSAGE_CONTENT,
            message_id=message_id,
            delta=f"[错误] 所有模型都无法生成有效的SQL，请手动检查查询需求或联系管理员\n"
        ))
    
    def _generate_dynamic_system_prompt(self, attempt: int = 0) -> str:
        """生成动态的system提示词，避免缓存"""
        base_prompts = [
            "你是一个专业的达梦SQL生成助手。",
            "作为经验丰富的达梦数据库专家，请协助生成SQL查询。", 
            "现在需要你扮演资深的达梦SQL开发工程师角色。",
            "请以精通达梦数据库的SQL专家身份来处理这个任务。",
            "担任专业的达梦数据库分析师，协助完成SQL生成工作。",
            "基于你在达梦数据库方面的专业知识，请生成相应的SQL语句。",
            "以达梦数据库技术专家的角色，为用户提供SQL查询支持。",
            "请运用你的达梦SQL开发经验来解决这个数据查询需求。"
        ]
        
        # 根据尝试次数选择不同的提示词，避免缓存
        prompt_index = attempt % len(base_prompts)
        return base_prompts[prompt_index]
    
    async def run(self, input_data) -> AsyncGenerator[str, None]:
        """运行智能体的主方法"""
        print(f"[DEBUG] 数据分析Agent run() 开始执行")
        
        encoder = EventEncoder()
        
        # 获取必要的ID信息
        thread_id = input_data.thread_id
        run_id = input_data.run_id
        message_id = str(uuid.uuid4())
        
        print(f"[DEBUG] thread_id: {thread_id}, run_id: {run_id}")
        
        # 发送运行开始事件
        yield encoder.encode(RunStartedEvent(
            type=EventType.RUN_STARTED,
            thread_id=thread_id,
            run_id=run_id
        ))
        
        # 发送消息开始事件
        yield encoder.encode(TextMessageStartEvent(
            type=EventType.TEXT_MESSAGE_START,
            message_id=message_id,
            role="assistant"
        ))
        
        try:
            # 1. 意图识别
            intent = await self._recognize_intent(input_data.messages)
            print(f"[DEBUG] 识别意图: {intent}")
            
            # 2. 根据意图执行SQL生成和执行逻辑
            async for chunk in self._handle_sql_generation_and_execution(
                input_data.messages, intent, encoder, message_id, thread_id, run_id
            ):
                yield chunk
                        
        except TaskCancelledException as e:
            print(f"[DEBUG] 任务被取消: {str(e)}")
            yield encoder.encode(TextMessageContentEvent(
                type=EventType.TEXT_MESSAGE_CONTENT,
                message_id=message_id,
                delta="[日志] 任务已取消\n"
            ))
        except Exception as e:
            print(f"[DEBUG] 捕获到异常: {type(e).__name__}: {str(e)}")
            import traceback
            print(f"[DEBUG] 异常堆栈: {traceback.format_exc()}")
            yield encoder.encode(TextMessageContentEvent(
                type=EventType.TEXT_MESSAGE_CONTENT,
                message_id=message_id,
                delta=f"[错误] 执行过程中发生错误: {str(e)}\n"
            ))
        
        # 发送消息结束事件
        yield encoder.encode(TextMessageEndEvent(
            type=EventType.TEXT_MESSAGE_END,
            message_id=message_id
        ))
        
        # 发送运行结束事件
        yield encoder.encode(RunFinishedEvent(
            type=EventType.RUN_FINISHED,
            thread_id=thread_id,
            run_id=run_id
        ))
        
        # 清理取消状态
        clear_cancelled_task(thread_id, run_id)
        
        # 注意：这里不清理缓存，因为用户可能继续在同一会话中进行操作
        # 如果需要清理缓存，可以在会话真正结束时调用 self._clear_cache(thread_id)


app = FastAPI()

@app.post("/api/cancel")
async def api_cancel(request: Request):
    data = await request.json()
    thread_id = data.get("thread_id")
    run_id = data.get("run_id")
    if not thread_id or not run_id:
        return JSONResponse({"success": False, "msg": "thread_id和run_id必填"}, status_code=400)
    cancel_task(thread_id, run_id)
    return {"success": True, "msg": "已取消"}

@app.post("/api/clear_cache")
async def api_clear_cache(request: Request):
    """清理指定线程的缓存"""
    data = await request.json()
    thread_id = data.get("thread_id")
    if not thread_id:
        return JSONResponse({"success": False, "msg": "thread_id必填"}, status_code=400)
    
    agent = get_agent_instance()
    agent._clear_cache(thread_id)
    return {"success": True, "msg": f"已清理线程 {thread_id} 的缓存"}