# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
智能体工具函数模块
"""

import re


def strip_agent_mention(content: str) -> str:
    """
    去除消息内容中的智能体提及

    Args:
        content: 原始消息内容，可能包含@智能体名称

    Returns:
        去除智能体提及后的消息内容

    Examples:
        >>> strip_agent_mention("@数据分析智能体 查询工作中心数据")
        "查询工作中心数据"
        >>> strip_agent_mention("@默认智能体 你好")
        "你好"
    """
    if not content:
        return content

    # 去除以@开头的智能体提及
    # 匹配模式：@智能体名称 后面跟空格，然后是实际内容
    pattern = r'^@[^\s]+\s+'
    result = re.sub(pattern, '', content.strip())

    # 添加调试日志
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"strip_agent_mention: 输入='{content}' -> 输出='{result}'")

    return result.strip()