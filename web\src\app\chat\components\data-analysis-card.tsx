// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { motion, AnimatePresence } from "framer-motion";
import {
  Database,
  AlertCircle,
  CheckCircle,
  Play,
  Search,
  Code,
  Table,
  BarChart3,
  Zap,
  Clock,
  ChevronDown,
  ChevronUp,
  Copy,
  Download
} from "lucide-react";
import React, { useMemo, useState } from "react";

import { Markdown } from "~/components/deer-flow/markdown";
import { RainbowText } from "~/components/deer-flow/rainbow-text";
import { LoadingAnimation } from "~/components/deer-flow/loading-animation";
import { BorderBeam } from "~/components/magicui/border-beam";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Separator } from "~/components/ui/separator";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "~/components/ui/collapsible";
import {
  Table as UITable,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { ScrollArea } from "~/components/ui/scroll-area";
import type { Message } from "~/core/messages";
import { cn } from "~/lib/utils";

interface TableData {
  type: "table";
  data: Record<string, any>[];
  total: number;
}

interface AnalysisStep {
  type: "keyword_extraction" | "schema_analysis" | "sql_generation" | "execution" | "result";
  title: string;
  content: string;
  status: "pending" | "running" | "completed" | "error";
  timestamp?: string;
  data?: any;
}

function DataAnalysisCard({
  className,
  message,
}: {
  className?: string;
  message: Message;
}) {
  const [expandedSteps, setExpandedSteps] = useState<Set<number>>(new Set());
  const [showRawData, setShowRawData] = useState(false);

  // 解析消息内容，提取分析步骤和数据
  const { analysisSteps, tableData, hasError, sqlQuery } = useMemo(() => {
    const content = message.content ?? "";
    const hasError = content.includes("[错误]");

    // 解析分析步骤
    const steps: AnalysisStep[] = [];
    const lines = content.split('\n');

    let currentStep: AnalysisStep | null = null;

    for (const line of lines) {
      if (line.includes('[日志]')) {
        const logContent = line.replace('[日志]', '').trim();

        if (logContent.includes('正在提取关键词')) {
          currentStep = {
            type: "keyword_extraction",
            title: "关键词提取",
            content: logContent,
            status: message.isStreaming ? "running" : "completed"
          };
          steps.push(currentStep);
        } else if (logContent.includes('正在获取数据库结构')) {
          currentStep = {
            type: "schema_analysis",
            title: "数据库结构分析",
            content: logContent,
            status: message.isStreaming ? "running" : "completed"
          };
          steps.push(currentStep);
        } else if (logContent.includes('正在生成SQL')) {
          currentStep = {
            type: "sql_generation",
            title: "SQL查询生成",
            content: logContent,
            status: message.isStreaming ? "running" : "completed"
          };
          steps.push(currentStep);
        } else if (logContent.includes('正在执行SQL')) {
          currentStep = {
            type: "execution",
            title: "查询执行",
            content: logContent,
            status: message.isStreaming ? "running" : "completed"
          };
          steps.push(currentStep);
        } else if (currentStep) {
          currentStep.content += '\n' + logContent;
        }
      } else if (line.includes('[错误]')) {
        if (currentStep) {
          currentStep.status = "error";
          currentStep.content += '\n' + line.replace('[错误]', '').trim();
        }
      }
    }

    // 提取SQL查询
    const sqlMatch = content.match(/```sql\n(.*?)\n```/s);
    const sqlQuery = sqlMatch?.[1] || null;

    // 提取表格数据
    const tableMatch = content.match(/\[TABLE_DATA\]\s*(\{.*?\})/s);
    let tableData: TableData | null = null;

    if (tableMatch && tableMatch[1]) {
      try {
        tableData = JSON.parse(tableMatch[1]);
        if (tableData && tableData.data) {
          steps.push({
            type: "result",
            title: "查询结果",
            content: `成功获取 ${tableData.total} 条记录`,
            status: "completed",
            data: tableData
          });
        }
      } catch (e) {
        console.error("Failed to parse table data:", e);
      }
    }

    return { analysisSteps: steps, tableData, hasError, sqlQuery };
  }, [message.content, message.isStreaming]);

  // 获取状态信息
  const statusInfo = useMemo(() => {
    if (hasError) {
      return {
        icon: AlertCircle,
        color: "text-red-500",
        bgColor: "bg-red-50 dark:bg-red-950/20",
        borderColor: "border-red-200 dark:border-red-800",
        status: "错误"
      };
    }

    if (message.isStreaming) {
      return {
        icon: Play,
        color: "text-blue-500",
        bgColor: "bg-blue-50 dark:bg-blue-950/20",
        borderColor: "border-blue-200 dark:border-blue-800",
        status: "分析中"
      };
    }

    return {
      icon: CheckCircle,
      color: "text-green-500",
      bgColor: "bg-green-50 dark:bg-green-950/20",
      borderColor: "border-green-200 dark:border-green-800",
      status: "完成"
    };
  }, [hasError, message.isStreaming]);

  const toggleStep = (index: number) => {
    const newExpanded = new Set(expandedSteps);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedSteps(newExpanded);
  };

  const StatusIcon = statusInfo.icon;

  return (
    <motion.div
      className={cn("w-full", className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <Card className={cn(
        "w-full transition-all duration-200 relative overflow-hidden",
        statusInfo.borderColor,
        statusInfo.bgColor
      )}>
        {/* 动态边框效果 */}
        {message.isStreaming && (
          <BorderBeam
            size={60}
            duration={4}
            colorFrom="#3b82f6"
            colorTo="#8b5cf6"
          />
        )}

        <CardHeader className="pb-4">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="relative">
                <Database className="h-6 w-6 text-primary" />
                {message.isStreaming && (
                  <motion.div
                    className="absolute -inset-1 rounded-full bg-blue-500/20"
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                )}
              </div>
              <div className="flex flex-col">
                <RainbowText
                  className="text-lg font-semibold"
                  animated={message.isStreaming}
                >
                  数据分析智能体
                </RainbowText>
                <div className="flex items-center gap-2 mt-1">
                  <Badge
                    variant={hasError ? "destructive" : message.isStreaming ? "default" : "secondary"}
                    className="text-xs"
                  >
                    <StatusIcon className="h-3 w-3 mr-1" />
                    {statusInfo.status}
                  </Badge>
                  {analysisSteps.length > 0 && (
                    <Badge variant="outline" className="text-xs">
                      {analysisSteps.filter(s => s.status === "completed").length}/{analysisSteps.length} 步骤
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center gap-2">
              {sqlQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigator.clipboard.writeText(sqlQuery)}
                  className="h-8 w-8 p-0"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              )}
              {tableData && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowRawData(!showRawData)}
                  className="h-8 w-8 p-0"
                >
                  <Code className="h-4 w-4" />
                </Button>
              )}
            </div>
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* 分析步骤展示 */}
          {analysisSteps.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <BarChart3 className="h-4 w-4" />
                分析流程
              </div>

              <div className="space-y-2">
                {analysisSteps.map((step, index) => (
                  <AnalysisStepCard
                    key={index}
                    step={step}
                    index={index}
                    isExpanded={expandedSteps.has(index)}
                    onToggle={() => toggleStep(index)}
                    isStreaming={message.isStreaming && index === analysisSteps.length - 1}
                  />
                ))}
              </div>
            </div>
          )}

          {/* SQL查询展示 */}
          {sqlQuery && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="space-y-2"
            >
              <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <Code className="h-4 w-4" />
                生成的SQL查询
              </div>
              <div className="relative">
                <pre className="bg-muted/50 border rounded-lg p-4 text-sm overflow-x-auto">
                  <code className="language-sql">{sqlQuery}</code>
                </pre>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigator.clipboard.writeText(sqlQuery)}
                  className="absolute top-2 right-2 h-8 w-8 p-0"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </motion.div>
          )}

          {/* 表格数据展示 */}
          {tableData && tableData.data && tableData.data.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="space-y-3"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                  <Table className="h-4 w-4" />
                  查询结果
                  <Badge variant="secondary" className="text-xs">
                    {tableData.total} 条记录
                  </Badge>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowRawData(!showRawData)}
                    className="text-xs"
                  >
                    {showRawData ? "表格视图" : "原始数据"}
                  </Button>
                </div>
              </div>

              <AnimatePresence mode="wait">
                {showRawData ? (
                  <motion.div
                    key="raw"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="bg-muted/30 border rounded-lg p-4"
                  >
                    <ScrollArea className="h-64">
                      <pre className="text-xs font-mono">
                        {JSON.stringify(tableData.data, null, 2)}
                      </pre>
                    </ScrollArea>
                  </motion.div>
                ) : (
                  <motion.div
                    key="table"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    <DataTable data={tableData.data} />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          )}

          {/* 流式加载动画 */}
          {message.isStreaming && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex items-center justify-center py-4"
            >
              <LoadingAnimation className="scale-75" />
            </motion.div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}

// 分析步骤卡片组件
function AnalysisStepCard({
  step,
  index,
  isExpanded,
  onToggle,
  isStreaming
}: {
  step: AnalysisStep;
  index: number;
  isExpanded: boolean;
  onToggle: () => void;
  isStreaming?: boolean;
}) {
  const getStepIcon = (type: AnalysisStep['type']) => {
    switch (type) {
      case "keyword_extraction": return Search;
      case "schema_analysis": return Database;
      case "sql_generation": return Code;
      case "execution": return Zap;
      case "result": return Table;
      default: return Clock;
    }
  };

  const getStatusColor = (status: AnalysisStep['status']) => {
    switch (status) {
      case "completed": return "text-green-500";
      case "running": return "text-blue-500";
      case "error": return "text-red-500";
      default: return "text-gray-400";
    }
  };

  const StepIcon = getStepIcon(step.type);

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: index * 0.1 }}
      className="border rounded-lg overflow-hidden"
    >
      <Collapsible open={isExpanded} onOpenChange={onToggle}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-4 h-auto hover:bg-muted/50"
          >
            <div className="flex items-center gap-3">
              <div className="relative">
                <StepIcon className={cn("h-4 w-4", getStatusColor(step.status))} />
                {isStreaming && step.status === "running" && (
                  <motion.div
                    className="absolute -inset-1 rounded-full bg-blue-500/20"
                    animate={{ scale: [1, 1.3, 1] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  />
                )}
              </div>
              <div className="flex flex-col items-start">
                <span className="font-medium text-sm">{step.title}</span>
                <span className="text-xs text-muted-foreground">
                  {step.status === "running" ? "执行中..." :
                   step.status === "completed" ? "已完成" :
                   step.status === "error" ? "执行失败" : "等待中"}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {step.status === "completed" && (
                <CheckCircle className="h-4 w-4 text-green-500" />
              )}
              {step.status === "error" && (
                <AlertCircle className="h-4 w-4 text-red-500" />
              )}
              {step.status === "running" && isStreaming && (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  <Play className="h-4 w-4 text-blue-500" />
                </motion.div>
              )}
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </Button>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <div className="px-4 pb-4 pt-0">
            <Separator className="mb-3" />
            <div className="text-sm text-muted-foreground whitespace-pre-wrap">
              {step.content}
            </div>
            {step.data && step.type === "result" && (
              <div className="mt-3 p-3 bg-muted/30 rounded-md">
                <div className="text-xs font-medium text-muted-foreground mb-2">
                  数据预览
                </div>
                <div className="text-xs font-mono">
                  {JSON.stringify(step.data.data?.slice(0, 2), null, 2)}
                  {step.data.data?.length > 2 && "\n..."}
                </div>
              </div>
            )}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </motion.div>
  );
}

// 数据表格组件
function DataTable({ data }: { data: Record<string, any>[] }) {
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        暂无数据
      </div>
    );
  }

  const columns = Object.keys(data[0]);
  const displayData = data.slice(0, 10); // 只显示前10条

  return (
    <div className="border rounded-lg overflow-hidden">
      <ScrollArea className="h-64">
        <UITable>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column} className="font-medium">
                  {column}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {displayData.map((row, index) => (
              <motion.tr
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="border-b"
              >
                {columns.map((column) => (
                  <TableCell key={column} className="text-sm">
                    {typeof row[column] === 'object'
                      ? JSON.stringify(row[column])
                      : String(row[column] ?? '')
                    }
                  </TableCell>
                ))}
              </motion.tr>
            ))}
          </TableBody>
        </UITable>
      </ScrollArea>

      {data.length > 10 && (
        <div className="p-3 bg-muted/30 text-center text-xs text-muted-foreground">
          显示前 10 条，共 {data.length} 条记录
        </div>
      )}
    </div>
  );
}

export { DataAnalysisCard };