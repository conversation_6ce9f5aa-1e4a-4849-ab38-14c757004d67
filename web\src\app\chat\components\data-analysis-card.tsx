// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { motion } from "framer-motion";  
import { Database, AlertCircle, CheckCircle, Play } from "lucide-react";
import React, { useMemo } from "react";

import { Markdown } from "~/components/deer-flow/markdown";
import { RainbowText } from "~/components/deer-flow/rainbow-text";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
// Note: Table components might not exist yet - will be handled in message-list-view.tsx integration
import type { Message } from "~/core/messages";
import { cn } from "~/lib/utils";

interface TableData {
  type: "table";
  data: Record<string, any>[];
  total: number;
}

function DataAnalysisCard({
  className,
  message,
}: {
  className?: string;
  message: Message;
}) {
  // 解析消息内容中的表格数据
  const { displayContent, tableData, hasError } = useMemo(() => {
    const content = message.content ?? "";
    
    // 检查是否有错误
    const hasError = content.includes("[错误]");
    
    // 查找表格数据标记
    const tableMatch = content.match(/\[TABLE_DATA\]\s*(\{.*?\})/s);
    let tableData: TableData | null = null;
    let displayContent = content;
    
    if (tableMatch && tableMatch[1]) {
      try {
        tableData = JSON.parse(tableMatch[1]);
        // 移除显示内容中的表格数据标记
        displayContent = content.replace(/\[TABLE_DATA\]\s*\{.*?\}/s, "");
      } catch (e) {
        console.error("Failed to parse table data:", e);
      }
    }
    
    return { displayContent, tableData, hasError };
  }, [message.content]);
  
  // 获取状态图标和颜色
  const statusInfo = useMemo(() => {
    if (hasError) {
      return {
        icon: AlertCircle,
        color: "text-red-500",
        bgColor: "bg-red-50 dark:bg-red-950/20",
        borderColor: "border-red-200 dark:border-red-800"
      };
    }
    
    if (message.isStreaming) {
      return {
        icon: Play,
        color: "text-blue-500",
        bgColor: "bg-blue-50 dark:bg-blue-950/20", 
        borderColor: "border-blue-200 dark:border-blue-800"
      };
    }
    
    return {
      icon: CheckCircle,
      color: "text-green-500",
      bgColor: "bg-green-50 dark:bg-green-950/20",
      borderColor: "border-green-200 dark:border-green-800"
    };
  }, [hasError, message.isStreaming]);
  
  const StatusIcon = statusInfo.icon;
  
  return (
    <motion.div
      className={cn("w-full", className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <Card className={cn(
        "w-full transition-all duration-200",
        statusInfo.borderColor,
        statusInfo.bgColor
      )}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5 text-primary" />
            <RainbowText animated={message.isStreaming}>
              数据分析智能体
            </RainbowText>
            <StatusIcon className={cn("h-4 w-4", statusInfo.color)} />
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* 主要内容显示 */}
          <div className="flex w-full flex-col text-wrap break-words">
            <Markdown 
              className="prose dark:prose-invert max-w-none"
              animated={message.isStreaming}
            >
              {displayContent}
            </Markdown>
          </div>
          
          {/* 表格数据显示 - 简化版本 */}
          {tableData && tableData.data && tableData.data.length > 0 && (
            <div className="mt-4">
              <div className="mb-2 text-sm text-muted-foreground">
                查询结果 (共 {tableData.total} 条记录)
              </div>
              <div className="rounded-md border p-4 bg-muted/30">
                <div className="text-sm">
                  <div className="font-mono">
                    {JSON.stringify(tableData.data, null, 2)}
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}

export { DataAnalysisCard }; 