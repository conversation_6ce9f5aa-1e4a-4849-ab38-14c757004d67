[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "deer-flow"
version = "0.1.0"
description = "DeerFlow project"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "httpx>=0.28.1",
    "langchain-community>=0.3.19",
    "langchain-experimental>=0.3.4",
    "langchain-openai>=0.3.8",
    "langgraph>=0.3.5",
    "readabilipy>=0.3.0",
    "python-dotenv>=1.0.1",
    "socksio>=1.0.0",
    "markdownify>=1.1.0",
    "fastapi>=0.110.0",
    "uvicorn>=0.27.1",
    "sse-starlette>=1.6.5",
    "pandas>=2.2.3",
    "numpy>=2.2.3",
    "yfinance>=0.2.54",
    "litellm>=1.63.11",
    "json-repair>=0.7.0",
    "jinja2>=3.1.3",
    "duckduckgo-search>=8.0.0",
    "inquirerpy>=0.3.4",
    "arxiv>=2.2.0",
    "mcp>=1.6.0",
    "langchain-mcp-adapters>=0.0.9",
    "langchain-deepseek>=0.1.3",
    # 数据分析智能体依赖
    "aiohttp>=3.8.0",
    "sqlparse>=0.4.0",
    "neo4j>=5.0.0",
]

[project.optional-dependencies]
dev = [
    "black>=24.2.0",
    "langgraph-cli[inmem]>=0.2.10",
]
test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=1.0.0",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=src --cov-report=term-missing"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
]

[tool.coverage.report]
fail_under = 25

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ["py312"]
include = '\.pyi?$'
extend-exclude = '''
# A regex preceded with ^/ will apply only to files and directories
# in the root of the project.
^/build/
'''

[dependency-groups]
dev = [
    "pytest>=8.3.5",
    "pytest-cov>=6.0.0",
]
