{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/deer-flow/rainbow-text.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"animated\": \"rainbow-text-module__D-e5SG__animated\",\n  \"textShine\": \"rainbow-text-module__D-e5SG__textShine\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/deer-flow/rainbow-text.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport styles from \"./rainbow-text.module.css\";\r\n\r\nexport function RainbowText({\r\n  animated,\r\n  className,\r\n  children,\r\n}: {\r\n  animated?: boolean;\r\n  className?: string;\r\n  children?: React.ReactNode;\r\n}) {\r\n  return (\r\n    <span className={cn(animated && styles.animated, className)}>\r\n      {children}\r\n    </span>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;;;;AAEO,SAAS,YAAY,EAC1B,QAAQ,EACR,SAAS,EACT,QAAQ,EAKT;IACC,qBACE,sSAAC;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY,mKAAA,CAAA,UAAM,CAAC,QAAQ,EAAE;kBAC9C;;;;;;AAGP;KAdgB", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/ui/card.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;;;;;AAI/B;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/core/api/config.ts"], "sourcesContent": ["import { type DeerFlowConfig } from \"../config/types\";\r\n\r\nimport { resolveServiceURL } from \"./resolve-service-url\";\r\n\r\ndeclare global {\r\n  interface Window {\r\n    __deerflowConfig: DeerFlowConfig;\r\n  }\r\n}\r\n\r\nexport async function loadConfig() {\r\n  const res = await fetch(resolveServiceURL(\"./config\"));\r\n  const config = await res.json();\r\n  return config;\r\n}\r\n\r\nexport function getConfig(): DeerFlowConfig {\r\n  if (\r\n    typeof window === \"undefined\" ||\r\n    typeof window.__deerflowConfig === \"undefined\"\r\n  ) {\r\n    throw new Error(\"Config not loaded\");\r\n  }\r\n  return window.__deerflowConfig;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;;AAQO,eAAe;IACpB,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,kJAAA,CAAA,oBAAiB,AAAD,EAAE;IAC1C,MAAM,SAAS,MAAM,IAAI,IAAI;IAC7B,OAAO;AACT;AAEO,SAAS;IACd,IACE,aAAkB,eAClB,OAAO,OAAO,gBAAgB,KAAK,aACnC;QACA,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,OAAO,gBAAgB;AAChC", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/core/api/hooks.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { useEffect, useRef, useState } from \"react\";\r\n\r\nimport { env } from \"~/env\";\r\n\r\nimport { useReplay } from \"../replay\";\r\n\r\nimport { fetchReplayTitle } from \"./chat\";\r\nimport { getConfig } from \"./config\";\r\n\r\nexport function useReplayMetadata() {\r\n  const { isReplay } = useReplay();\r\n  const [title, setTitle] = useState<string | null>(null);\r\n  const isLoading = useRef(false);\r\n  const [error, setError] = useState<boolean>(false);\r\n  useEffect(() => {\r\n    if (!isReplay) {\r\n      return;\r\n    }\r\n    if (title || isLoading.current) {\r\n      return;\r\n    }\r\n    isLoading.current = true;\r\n    fetchReplayTitle()\r\n      .then((title) => {\r\n        setError(false);\r\n        setTitle(title ?? null);\r\n        if (title) {\r\n          document.title = `${title} - DeerFlow`;\r\n        }\r\n      })\r\n      .catch(() => {\r\n        setError(true);\r\n        setTitle(\"Error: the replay is not available.\");\r\n        document.title = \"DeerFlow\";\r\n      })\r\n      .finally(() => {\r\n        isLoading.current = false;\r\n      });\r\n  }, [isLoading, isReplay, title]);\r\n  return { title, isLoading, hasError: error };\r\n}\r\n\r\nexport function useRAGProvider() {\r\n  const [loading, setLoading] = useState(true);\r\n  const [provider, setProvider] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    if (env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY) {\r\n      setLoading(false);\r\n      return;\r\n    }\r\n    setProvider(getConfig().rag.provider);\r\n    setLoading(false);\r\n  }, []);\r\n\r\n  return { provider, loading };\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;AAEA;AAAA;AAEA;AACA;;;;;;;AAEO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,YAAY,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW;IAC5C,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,UAAU;gBACb;YACF;YACA,IAAI,SAAS,UAAU,OAAO,EAAE;gBAC9B;YACF;YACA,UAAU,OAAO,GAAG;YACpB,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,IACZ,IAAI;+CAAC,CAAC;oBACL,SAAS;oBACT,SAAS,SAAS;oBAClB,IAAI,OAAO;wBACT,SAAS,KAAK,GAAG,GAAG,MAAM,WAAW,CAAC;oBACxC;gBACF;8CACC,KAAK;+CAAC;oBACL,SAAS;oBACT,SAAS;oBACT,SAAS,KAAK,GAAG;gBACnB;8CACC,OAAO;+CAAC;oBACP,UAAU,OAAO,GAAG;gBACtB;;QACJ;sCAAG;QAAC;QAAW;QAAU;KAAM;IAC/B,OAAO;QAAE;QAAO;QAAW,UAAU;IAAM;AAC7C;GA/BgB;;QACO,iIAAA,CAAA,YAAS;;;AAgCzB,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAExD,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,6GAAA,CAAA,MAAG,CAAC,+BAA+B,EAAE;gBACvC,WAAW;gBACX;YACF;YACA,YAAY,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD,IAAI,GAAG,CAAC,QAAQ;YACpC,WAAW;QACb;mCAAG,EAAE;IAEL,OAAO;QAAE;QAAU;IAAQ;AAC7B;IAdgB", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/app/chat/components/welcome.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { motion } from \"framer-motion\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport function Welcome({ className }: { className?: string }) {\r\n  return (\r\n    <motion.div\r\n      className={cn(\"flex flex-col\", className)}\r\n      style={{ transition: \"all 0.2s ease-out\" }}\r\n      initial={{ opacity: 0, scale: 0.85 }}\r\n      animate={{ opacity: 1, scale: 1 }}\r\n    >\r\n      <h3 className=\"mb-2 text-center text-3xl font-medium\">\r\n        👋 Hello, there!\r\n      </h3>\r\n      <div className=\"text-muted-foreground px-4 text-center text-lg\">\r\n        Welcome to{\" \"}\r\n        <a\r\n          href=\"https://github.com/bytedance/deer-flow\"\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          className=\"hover:underline\"\r\n        >\r\n          🦌 <PERSON><PERSON><PERSON>\r\n        </a>\r\n        , a deep research assistant built on cutting-edge language models, helps\r\n        you search on web, browse information, and handle complex tasks.\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;;;;AAEO,SAAS,QAAQ,EAAE,SAAS,EAA0B;IAC3D,qBACE,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC/B,OAAO;YAAE,YAAY;QAAoB;QACzC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAK;QACnC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;;0BAEhC,sSAAC;gBAAG,WAAU;0BAAwC;;;;;;0BAGtD,sSAAC;gBAAI,WAAU;;oBAAiD;oBACnD;kCACX,sSAAC;wBACC,MAAK;wBACL,QAAO;wBACP,KAAI;wBACJ,WAAU;kCACX;;;;;;oBAEG;;;;;;;;;;;;;AAMZ;KA1BgB", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/app/chat/components/conversation-starter.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { motion } from \"framer-motion\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport { Welcome } from \"./welcome\";\r\n\r\nconst questions = [\r\n  \"How many times taller is the Eiffel Tower than the tallest building in the world?\",\r\n  \"How many years does an average Tesla battery last compared to a gasoline engine?\",\r\n  \"How many liters of water are required to produce 1 kg of beef?\",\r\n  \"How many times faster is the speed of light compared to the speed of sound?\",\r\n];\r\nexport function ConversationStarter({\r\n  className,\r\n  onSend,\r\n}: {\r\n  className?: string;\r\n  onSend?: (message: string) => void;\r\n}) {\r\n  return (\r\n    <div className={cn(\"flex flex-col items-center\", className)}>\r\n      <div className=\"pointer-events-none fixed inset-0 flex items-center justify-center\">\r\n        <Welcome className=\"pointer-events-auto mb-15 w-[75%] -translate-y-24\" />\r\n      </div>\r\n      <ul className=\"flex flex-wrap\">\r\n        {questions.map((question, index) => (\r\n          <motion.li\r\n            key={question}\r\n            className=\"flex w-1/2 shrink-0 p-2 active:scale-105\"\r\n            style={{ transition: \"all 0.2s ease-out\" }}\r\n            initial={{ opacity: 0, y: 24 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -20 }}\r\n            transition={{\r\n              duration: 0.2,\r\n              delay: index * 0.1 + 0.5,\r\n              ease: \"easeOut\",\r\n            }}\r\n          >\r\n            <div\r\n              className=\"bg-card text-muted-foreground cursor-pointer rounded-2xl border px-4 py-4 opacity-75 transition-all duration-300 hover:opacity-100 hover:shadow-md\"\r\n              onClick={() => {\r\n                onSend?.(question);\r\n              }}\r\n            >\r\n              {question}\r\n            </div>\r\n          </motion.li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;AAEA;;;;;AAEA,MAAM,YAAY;IAChB;IACA;IACA;IACA;CACD;AACM,SAAS,oBAAoB,EAClC,SAAS,EACT,MAAM,EAIP;IACC,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;;0BAC/C,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,+IAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;0BAErB,sSAAC;gBAAG,WAAU;0BACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;wBAER,WAAU;wBACV,OAAO;4BAAE,YAAY;wBAAoB;wBACzC,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,YAAY;4BACV,UAAU;4BACV,OAAO,QAAQ,MAAM;4BACrB,MAAM;wBACR;kCAEA,cAAA,sSAAC;4BACC,WAAU;4BACV,SAAS;gCACP,SAAS;4BACX;sCAEC;;;;;;uBAlBE;;;;;;;;;;;;;;;;AAyBjB;KAxCgB", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/deer-flow/icons/detective.tsx"], "sourcesContent": ["export function Detective({ className }: { className?: string }) {\r\n  return (\r\n    <svg\r\n      className={className}\r\n      version=\"1.1\"\r\n      width=\"800px\"\r\n      height=\"800px\"\r\n      viewBox=\"0 0 512 512\"\r\n    >\r\n      <g fill=\"currentcolor\">\r\n        <path\r\n          d=\"M392.692,257.322c-1.172-8.125-2.488-16.98-3.807-25.984c-5.856-39.012-12.59-81.688-14.86-87.832\r\n\t\tc-4.318-11.715-18.371-44.723-68.217-25.984c-15.738,5.926-18.812,11.93-41.648,8.93c-17.273-2.27-28.326-15.59-52.336-24.668\r\n\t\tc-49.844-18.883-71.584,11.711-75.902,23.422c-2.27,6.148-9.004,67.121-14.86,106.133c-1.39,8.86-2.633,17.566-3.804,25.621\r\n\t\tc37.256,7.535,84.174,12.879,138.705,12.879C309.541,269.837,355.801,264.716,392.692,257.322z\"\r\n        />\r\n        <path\r\n          d=\"M443.707,306.509c-8.051-2.196-16.834-4.246-26.057-6.148c-1.83-0.805-3.66-1.535-5.49-2.27h-0.072\r\n\t\tc-46.918,10.394-102.254,15.664-156.125,15.664c-53.652,0-108.768-5.27-155.541-15.516c-1.316,0.512-2.707,1.098-4.098,1.684\r\n\t\tc-8.858,1.828-17.348,3.73-25.106,5.781l-0.148,0.074C27.008,317.49,0,333.372,0,350.939c0,36.012,114.549,65.289,256.035,65.289\r\n\t\tc141.34,0,255.965-29.278,255.965-65.289C512,333.74,486.016,318.22,443.707,306.509z\"\r\n        />\r\n      </g>\r\n    </svg>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAO,SAAS,UAAU,EAAE,SAAS,EAA0B;IAC7D,qBACE,sSAAC;QACC,WAAW;QACX,SAAQ;QACR,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,sSAAC;YAAE,MAAK;;8BACN,sSAAC;oBACC,GAAE;;;;;;8BAKJ,sSAAC;oBACC,GAAE;;;;;;;;;;;;;;;;;AAQZ;KAzBgB", "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/deer-flow/resource-mentions.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { forwardRef, useEffect, useImperativeHandle, useState } from \"react\";\r\nimport type { Resource } from \"~/core/messages\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport interface ResourceMentionsProps {\r\n  items: Array<Resource>;\r\n  command: (item: { id: string; label: string }) => void;\r\n}\r\n\r\nexport const ResourceMentions = forwardRef<\r\n  { onKeyDown: (args: { event: KeyboardEvent }) => boolean },\r\n  ResourceMentionsProps\r\n>((props, ref) => {\r\n  const [selectedIndex, setSelectedIndex] = useState(0);\r\n\r\n  const selectItem = (index: number) => {\r\n    const item = props.items[index];\r\n\r\n    if (item) {\r\n      props.command({ id: item.uri, label: item.title });\r\n    }\r\n  };\r\n\r\n  const upHandler = () => {\r\n    setSelectedIndex(\r\n      (selectedIndex + props.items.length - 1) % props.items.length,\r\n    );\r\n  };\r\n\r\n  const downHandler = () => {\r\n    setSelectedIndex((selectedIndex + 1) % props.items.length);\r\n  };\r\n\r\n  const enterHandler = () => {\r\n    selectItem(selectedIndex);\r\n  };\r\n\r\n  useEffect(() => setSelectedIndex(0), [props.items]);\r\n\r\n  useImperativeHandle(ref, () => ({\r\n    onKeyDown: ({ event }) => {\r\n      if (event.key === \"ArrowUp\") {\r\n        upHandler();\r\n        return true;\r\n      }\r\n\r\n      if (event.key === \"ArrowDown\") {\r\n        downHandler();\r\n        return true;\r\n      }\r\n\r\n      if (event.key === \"Enter\") {\r\n        enterHandler();\r\n        return true;\r\n      }\r\n\r\n      return false;\r\n    },\r\n  }));\r\n\r\n  return (\r\n    <div className=\"bg-card border-var(--border) relative flex flex-col gap-1 overflow-auto rounded-md border p-2 shadow\">\r\n      {props.items.length ? (\r\n        props.items.map((item, index) => (\r\n          <button\r\n            className={cn(\r\n              \"focus-visible:ring-ring hover:bg-accent hover:text-accent-foreground inline-flex h-9 w-full items-center justify-start gap-2 rounded-md px-4 py-2 text-sm whitespace-nowrap transition-colors focus-visible:ring-1 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n              selectedIndex === index &&\r\n                \"bg-secondary text-secondary-foreground\",\r\n            )}\r\n            key={index}\r\n            onClick={() => selectItem(index)}\r\n          >\r\n            {item.title}\r\n          </button>\r\n        ))\r\n      ) : (\r\n        <div className=\"items-center justify-center text-gray-500\">\r\n          No result\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;;;;;AAOO,MAAM,iCAAmB,GAAA,CAAA,GAAA,sQAAA,CAAA,aAAU,AAAD,UAGvC,CAAC,OAAO;;IACR,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,MAAM,KAAK,CAAC,MAAM;QAE/B,IAAI,MAAM;YACR,MAAM,OAAO,CAAC;gBAAE,IAAI,KAAK,GAAG;gBAAE,OAAO,KAAK,KAAK;YAAC;QAClD;IACF;IAEA,MAAM,YAAY;QAChB,iBACE,CAAC,gBAAgB,MAAM,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,KAAK,CAAC,MAAM;IAEjE;IAEA,MAAM,cAAc;QAClB,iBAAiB,CAAC,gBAAgB,CAAC,IAAI,MAAM,KAAK,CAAC,MAAM;IAC3D;IAEA,MAAM,eAAe;QACnB,WAAW;IACb;IAEA,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;sCAAE,IAAM,iBAAiB;qCAAI;QAAC,MAAM,KAAK;KAAC;IAElD,CAAA,GAAA,sQAAA,CAAA,sBAAmB,AAAD,EAAE;gDAAK,IAAM,CAAC;gBAC9B,SAAS;4DAAE,CAAC,EAAE,KAAK,EAAE;wBACnB,IAAI,MAAM,GAAG,KAAK,WAAW;4BAC3B;4BACA,OAAO;wBACT;wBAEA,IAAI,MAAM,GAAG,KAAK,aAAa;4BAC7B;4BACA,OAAO;wBACT;wBAEA,IAAI,MAAM,GAAG,KAAK,SAAS;4BACzB;4BACA,OAAO;wBACT;wBAEA,OAAO;oBACT;;YACF,CAAC;;IAED,qBACE,sSAAC;QAAI,WAAU;kBACZ,MAAM,KAAK,CAAC,MAAM,GACjB,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,sSAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8VACA,kBAAkB,SAChB;gBAGJ,SAAS,IAAM,WAAW;0BAEzB,KAAK,KAAK;eAHN;;;;sCAOT,sSAAC;YAAI,WAAU;sBAA4C;;;;;;;;;;;AAMnE", "debugId": null}}, {"offset": {"line": 616, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/deer-flow/resource-suggestion.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport type { MentionOptions } from \"@tiptap/extension-mention\";\r\nimport { ReactRenderer } from \"@tiptap/react\";\r\nimport {\r\n  ResourceMentions,\r\n  type ResourceMentionsProps,\r\n} from \"./resource-mentions\";\r\nimport type { Instance, Props } from \"tippy.js\";\r\nimport tippy from \"tippy.js\";\r\nimport { resolveServiceURL } from \"~/core/api/resolve-service-url\";\r\nimport type { Resource } from \"~/core/messages\";\r\n\r\nexport const resourceSuggestion: MentionOptions[\"suggestion\"] = {\r\n  items: ({ query }) => {\r\n    return fetch(resolveServiceURL(`rag/resources?query=${query}`), {\r\n      method: \"GET\",\r\n    })\r\n      .then((res) => res.json())\r\n      .then((res) => {\r\n        return res.resources as Array<Resource>;\r\n      })\r\n      .catch((err) => {\r\n        return [];\r\n      });\r\n  },\r\n\r\n  render: () => {\r\n    let reactRenderer: ReactRenderer<\r\n      { onKeyDown: (args: { event: KeyboardEvent }) => boolean },\r\n      ResourceMentionsProps\r\n    >;\r\n    let popup: Instance<Props>[] | null = null;\r\n\r\n    return {\r\n      onStart: (props) => {\r\n        if (!props.clientRect) {\r\n          return;\r\n        }\r\n\r\n        reactRenderer = new ReactRenderer(ResourceMentions, {\r\n          props,\r\n          editor: props.editor,\r\n        });\r\n\r\n        popup = tippy(\"body\", {\r\n          getReferenceClientRect: props.clientRect as any,\r\n          appendTo: () => document.body,\r\n          content: reactRenderer.element,\r\n          showOnCreate: true,\r\n          interactive: true,\r\n          trigger: \"manual\",\r\n          placement: \"top-start\",\r\n        });\r\n      },\r\n\r\n      onUpdate(props) {\r\n        reactRenderer.updateProps(props);\r\n\r\n        if (!props.clientRect) {\r\n          return;\r\n        }\r\n\r\n        popup?.[0]?.setProps({\r\n          getReferenceClientRect: props.clientRect as any,\r\n        });\r\n      },\r\n\r\n      onKeyDown(props) {\r\n        if (props.event.key === \"Escape\") {\r\n          popup?.[0]?.hide();\r\n\r\n          return true;\r\n        }\r\n\r\n        return reactRenderer.ref?.onKeyDown(props) ?? false;\r\n      },\r\n\r\n      onExit() {\r\n        popup?.[0]?.destroy();\r\n        reactRenderer.destroy();\r\n      },\r\n    };\r\n  },\r\n};\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAG/B;AACA;AAKA;AACA;;;;;AAGO,MAAM,qBAAmD;IAC9D,OAAO,CAAC,EAAE,KAAK,EAAE;QACf,OAAO,MAAM,CAAA,GAAA,kJAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC,oBAAoB,EAAE,OAAO,GAAG;YAC9D,QAAQ;QACV,GACG,IAAI,CAAC,CAAC,MAAQ,IAAI,IAAI,IACtB,IAAI,CAAC,CAAC;YACL,OAAO,IAAI,SAAS;QACtB,GACC,KAAK,CAAC,CAAC;YACN,OAAO,EAAE;QACX;IACJ;IAEA,QAAQ;QACN,IAAI;QAIJ,IAAI,QAAkC;QAEtC,OAAO;YACL,SAAS,CAAC;gBACR,IAAI,CAAC,MAAM,UAAU,EAAE;oBACrB;gBACF;gBAEA,gBAAgB,IAAI,+QAAA,CAAA,gBAAa,CAAC,6JAAA,CAAA,mBAAgB,EAAE;oBAClD;oBACA,QAAQ,MAAM,MAAM;gBACtB;gBAEA,QAAQ,CAAA,GAAA,8MAAA,CAAA,UAAK,AAAD,EAAE,QAAQ;oBACpB,wBAAwB,MAAM,UAAU;oBACxC,UAAU,IAAM,SAAS,IAAI;oBAC7B,SAAS,cAAc,OAAO;oBAC9B,cAAc;oBACd,aAAa;oBACb,SAAS;oBACT,WAAW;gBACb;YACF;YAEA,UAAS,KAAK;gBACZ,cAAc,WAAW,CAAC;gBAE1B,IAAI,CAAC,MAAM,UAAU,EAAE;oBACrB;gBACF;gBAEA,OAAO,CAAC,EAAE,EAAE,SAAS;oBACnB,wBAAwB,MAAM,UAAU;gBAC1C;YACF;YAEA,WAAU,KAAK;gBACb,IAAI,MAAM,KAAK,CAAC,GAAG,KAAK,UAAU;oBAChC,OAAO,CAAC,EAAE,EAAE;oBAEZ,OAAO;gBACT;gBAEA,OAAO,cAAc,GAAG,EAAE,UAAU,UAAU;YAChD;YAEA;gBACE,OAAO,CAAC,EAAE,EAAE;gBACZ,cAAc,OAAO;YACvB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/core/api/agents.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { resolveServiceURL } from \"./resolve-service-url\";\r\n\r\nexport interface Agent {\r\n  name: string;\r\n  display_name: string;\r\n  description: string;\r\n}\r\n\r\nexport interface AgentsResponse {\r\n  agents: Agent[];\r\n}\r\n\r\nexport async function getAgents(): Promise<Agent[]> {\r\n  try {\r\n    const response = await fetch(resolveServiceURL(\"agents\"));\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n    const data: AgentsResponse = await response.json();\r\n    return data.agents;\r\n  } catch (error) {\r\n    console.error(\"Failed to fetch agents:\", error);\r\n    return [];\r\n  }\r\n} "], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;;AAYO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAA,GAAA,kJAAA,CAAA,oBAAiB,AAAD,EAAE;QAC/C,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,MAAM,OAAuB,MAAM,SAAS,IAAI;QAChD,OAAO,KAAK,MAAM;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,EAAE;IACX;AACF", "debugId": null}}, {"offset": {"line": 722, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/deer-flow/agent-suggestion.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { ReactRenderer } from \"@tiptap/react\";\r\nimport tippy from \"tippy.js\";\r\nimport { forwardRef, useEffect, useImperativeHandle, useState } from \"react\";\r\nimport { getAgents, type Agent } from \"~/core/api/agents\";\r\n\r\ninterface AgentSuggestionProps {\r\n  items: Agent[];\r\n  command: (props: { id: string; label: string }) => void;\r\n}\r\n\r\nconst AgentSuggestionList = forwardRef<\r\n  { onKeyDown: (props: { event: KeyboardEvent }) => boolean },\r\n  AgentSuggestionProps\r\n>((props, ref) => {\r\n  const [selectedIndex, setSelectedIndex] = useState(0);\r\n\r\n  const selectItem = (index: number) => {\r\n    const item = props.items[index];\r\n    if (item) {\r\n      props.command({ id: item.name, label: item.display_name });\r\n    }\r\n  };\r\n\r\n  const upHandler = () => {\r\n    setSelectedIndex(\r\n      (selectedIndex + props.items.length - 1) % props.items.length,\r\n    );\r\n  };\r\n\r\n  const downHandler = () => {\r\n    setSelectedIndex((selectedIndex + 1) % props.items.length);\r\n  };\r\n\r\n  const enterHandler = () => {\r\n    selectItem(selectedIndex);\r\n  };\r\n\r\n  useEffect(() => setSelectedIndex(0), [props.items]);\r\n\r\n  useImperativeHandle(ref, () => ({\r\n    onKeyDown: ({ event }) => {\r\n      if (event.key === \"ArrowUp\") {\r\n        upHandler();\r\n        return true;\r\n      }\r\n\r\n      if (event.key === \"ArrowDown\") {\r\n        downHandler();\r\n        return true;\r\n      }\r\n\r\n      if (event.key === \"Enter\") {\r\n        enterHandler();\r\n        return true;\r\n      }\r\n\r\n      return false;\r\n    },\r\n  }));\r\n\r\n  return (\r\n    <div className=\"bg-popover border-border max-h-60 overflow-auto rounded-md border p-1 shadow-md\">\r\n      {props.items.length ? (\r\n        props.items.map((item, index) => (\r\n          <button\r\n            className={`hover:bg-accent flex w-full items-start gap-2 rounded-sm px-2 py-1 text-left text-sm ${\r\n              index === selectedIndex ? \"bg-accent\" : \"\"\r\n            }`}\r\n            key={index}\r\n            onClick={() => selectItem(index)}\r\n          >\r\n            <div className=\"flex flex-col\">\r\n              <div className=\"font-medium\">{item.display_name}</div>\r\n              <div className=\"text-muted-foreground text-xs\">\r\n                {item.description}\r\n              </div>\r\n            </div>\r\n          </button>\r\n        ))\r\n      ) : (\r\n        <div className=\"text-muted-foreground px-2 py-1 text-sm\">\r\n          No agents found\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nAgentSuggestionList.displayName = \"AgentSuggestionList\";\r\n\r\nexport const agentSuggestion = {\r\n  items: async ({ query }: { query: string }) => {\r\n    const agents = await getAgents();\r\n    return agents.filter((agent) =>\r\n      agent.display_name.toLowerCase().includes(query.toLowerCase()),\r\n    );\r\n  },\r\n\r\n  render: () => {\r\n    let component: ReactRenderer<\r\n      { onKeyDown: (props: { event: KeyboardEvent }) => boolean },\r\n      AgentSuggestionProps\r\n    >;\r\n    let popup: any;\r\n\r\n    return {\r\n      onStart: (props: any) => {\r\n        component = new ReactRenderer(AgentSuggestionList, {\r\n          props,\r\n          editor: props.editor,\r\n        });\r\n\r\n        if (!props.clientRect) {\r\n          return;\r\n        }\r\n\r\n        popup = tippy(\"body\", {\r\n          getReferenceClientRect: props.clientRect,\r\n          appendTo: () => document.body,\r\n          content: component.element,\r\n          showOnCreate: true,\r\n          interactive: true,\r\n          trigger: \"manual\",\r\n          placement: \"bottom-start\",\r\n        });\r\n      },\r\n\r\n      onUpdate(props: any) {\r\n        component.updateProps(props);\r\n\r\n        if (!props.clientRect) {\r\n          return;\r\n        }\r\n\r\n        popup[0].setProps({\r\n          getReferenceClientRect: props.clientRect,\r\n        });\r\n      },\r\n\r\n      onKeyDown(props: any) {\r\n        if (props.event.key === \"Escape\") {\r\n          popup[0].hide();\r\n          return true;\r\n        }\r\n\r\n        return component.ref?.onKeyDown(props) ?? false;\r\n      },\r\n\r\n      onExit() {\r\n        popup[0].destroy();\r\n        component.destroy();\r\n      },\r\n    };\r\n  },\r\n}; "], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AACA;AACA;;;;;;;AAOA,MAAM,oCAAsB,GAAA,CAAA,GAAA,sQAAA,CAAA,aAAU,AAAD,UAGnC,CAAC,OAAO;;IACR,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,MAAM,KAAK,CAAC,MAAM;QAC/B,IAAI,MAAM;YACR,MAAM,OAAO,CAAC;gBAAE,IAAI,KAAK,IAAI;gBAAE,OAAO,KAAK,YAAY;YAAC;QAC1D;IACF;IAEA,MAAM,YAAY;QAChB,iBACE,CAAC,gBAAgB,MAAM,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,KAAK,CAAC,MAAM;IAEjE;IAEA,MAAM,cAAc;QAClB,iBAAiB,CAAC,gBAAgB,CAAC,IAAI,MAAM,KAAK,CAAC,MAAM;IAC3D;IAEA,MAAM,eAAe;QACnB,WAAW;IACb;IAEA,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;yCAAE,IAAM,iBAAiB;wCAAI;QAAC,MAAM,KAAK;KAAC;IAElD,CAAA,GAAA,sQAAA,CAAA,sBAAmB,AAAD,EAAE;mDAAK,IAAM,CAAC;gBAC9B,SAAS;+DAAE,CAAC,EAAE,KAAK,EAAE;wBACnB,IAAI,MAAM,GAAG,KAAK,WAAW;4BAC3B;4BACA,OAAO;wBACT;wBAEA,IAAI,MAAM,GAAG,KAAK,aAAa;4BAC7B;4BACA,OAAO;wBACT;wBAEA,IAAI,MAAM,GAAG,KAAK,SAAS;4BACzB;4BACA,OAAO;wBACT;wBAEA,OAAO;oBACT;;YACF,CAAC;;IAED,qBACE,sSAAC;QAAI,WAAU;kBACZ,MAAM,KAAK,CAAC,MAAM,GACjB,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,sSAAC;gBACC,WAAW,CAAC,qFAAqF,EAC/F,UAAU,gBAAgB,cAAc,IACxC;gBAEF,SAAS,IAAM,WAAW;0BAE1B,cAAA,sSAAC;oBAAI,WAAU;;sCACb,sSAAC;4BAAI,WAAU;sCAAe,KAAK,YAAY;;;;;;sCAC/C,sSAAC;4BAAI,WAAU;sCACZ,KAAK,WAAW;;;;;;;;;;;;eANhB;;;;sCAYT,sSAAC;YAAI,WAAU;sBAA0C;;;;;;;;;;;AAMjE;;AAEA,oBAAoB,WAAW,GAAG;AAE3B,MAAM,kBAAkB;IAC7B,OAAO,OAAO,EAAE,KAAK,EAAqB;QACxC,MAAM,SAAS,MAAM,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD;QAC7B,OAAO,OAAO,MAAM,CAAC,CAAC,QACpB,MAAM,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;IAE/D;IAEA,QAAQ;QACN,IAAI;QAIJ,IAAI;QAEJ,OAAO;YACL,SAAS,CAAC;gBACR,YAAY,IAAI,+QAAA,CAAA,gBAAa,CAAC,qBAAqB;oBACjD;oBACA,QAAQ,MAAM,MAAM;gBACtB;gBAEA,IAAI,CAAC,MAAM,UAAU,EAAE;oBACrB;gBACF;gBAEA,QAAQ,CAAA,GAAA,8MAAA,CAAA,UAAK,AAAD,EAAE,QAAQ;oBACpB,wBAAwB,MAAM,UAAU;oBACxC,UAAU,IAAM,SAAS,IAAI;oBAC7B,SAAS,UAAU,OAAO;oBAC1B,cAAc;oBACd,aAAa;oBACb,SAAS;oBACT,WAAW;gBACb;YACF;YAEA,UAAS,KAAU;gBACjB,UAAU,WAAW,CAAC;gBAEtB,IAAI,CAAC,MAAM,UAAU,EAAE;oBACrB;gBACF;gBAEA,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC;oBAChB,wBAAwB,MAAM,UAAU;gBAC1C;YACF;YAEA,WAAU,KAAU;gBAClB,IAAI,MAAM,KAAK,CAAC,GAAG,KAAK,UAAU;oBAChC,KAAK,CAAC,EAAE,CAAC,IAAI;oBACb,OAAO;gBACT;gBAEA,OAAO,UAAU,GAAG,EAAE,UAAU,UAAU;YAC5C;YAEA;gBACE,KAAK,CAAC,EAAE,CAAC,OAAO;gBAChB,UAAU,OAAO;YACnB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/deer-flow/message-input.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\n// SPDX-License-Identifier: MIT\n\n\"use client\";\n\nimport Mention from \"@tiptap/extension-mention\";\nimport { Editor, Extension, type Content } from \"@tiptap/react\";\nimport {\n  EditorContent,\n  type EditorInstance,\n  EditorRoot,\n  type JSONContent,\n  StarterKit,\n  Placeholder,\n} from \"novel\";\nimport { Markdown } from \"tiptap-markdown\";\nimport { useDebouncedCallback } from \"use-debounce\";\n\nimport \"~/styles/prosemirror.css\";\nimport { resourceSuggestion } from \"./resource-suggestion\";\nimport { agentSuggestion } from \"./agent-suggestion\";\nimport React, { forwardRef, useEffect, useMemo, useRef } from \"react\";\nimport type { Resource } from \"~/core/messages\";\nimport { useRAGProvider } from \"~/core/api/hooks\";\nimport { LoadingOutlined } from \"@ant-design/icons\";\n\nexport interface MessageInputRef {\n  focus: () => void;\n  submit: () => void;\n  setContent: (content: string) => void;\n}\n\nexport interface MessageInputProps {\n  className?: string;\n  placeholder?: string;\n  onChange?: (markdown: string) => void;\n  onEnter?: (message: string, resources: Array<Resource>) => void;\n}\n\nfunction formatMessage(content: JSONContent) {\n  if (content.content) {\n    const output: {\n      text: string;\n      resources: Array<Resource>;\n    } = {\n      text: \"\",\n      resources: [],\n    };\n    for (const node of content.content) {\n      const { text, resources } = formatMessage(node);\n      output.text += text;\n      output.resources.push(...resources);\n    }\n    return output;\n  } else {\n    return formatItem(content);\n  }\n}\n\nfunction formatItem(item: JSONContent): {\n  text: string;\n  resources: Array<Resource>;\n} {\n  if (item.type === \"text\") {\n    return { text: item.text ?? \"\", resources: [] };\n  }\n  if (item.type === \"mention\") {\n    // 检查是否是智能体mention\n    if (item.attrs?.id?.endsWith(\"_agent\")) {\n      return {\n        text: `@${item.attrs?.label} `,\n        resources: [],\n      };\n    }\n    // RAG资源mention\n    return {\n      text: `[${item.attrs?.label}](${item.attrs?.id})`,\n      resources: [\n        { uri: item.attrs?.id ?? \"\", title: item.attrs?.label ?? \"\" },\n      ],\n    };\n  }\n  return { text: \"\", resources: [] };\n}\n\nconst MessageInput = forwardRef<MessageInputRef, MessageInputProps>(\n  ({ className, onChange, onEnter }: MessageInputProps, ref) => {\n    const editorRef = useRef<Editor>(null);\n    const handleEnterRef = useRef<\n      ((message: string, resources: Array<Resource>) => void) | undefined\n    >(onEnter);\n    const debouncedUpdates = useDebouncedCallback(\n      async (editor: EditorInstance) => {\n        if (onChange) {\n          // Get the plain text content for prompt enhancement\n          const { text } = formatMessage(editor.getJSON() ?? []);\n          onChange(text);\n        }\n      },\n      200,\n    );\n\n    React.useImperativeHandle(ref, () => ({\n      focus: () => {\n        editorRef.current?.view.focus();\n      },\n      submit: () => {\n        if (onEnter) {\n          const { text, resources } = formatMessage(\n            editorRef.current?.getJSON() ?? [],\n          );\n          onEnter(text, resources);\n        }\n        editorRef.current?.commands.clearContent();\n      },\n      setContent: (content: string) => {\n        if (editorRef.current) {\n          editorRef.current.commands.setContent(content);\n          // 将光标定位到内容末尾，并确保有空格\n          setTimeout(() => {\n            if (editorRef.current) {\n              // 如果内容包含智能体mention，直接在末尾添加空格\n              if (content.includes(\"@默认智能体\") || content.includes(\"@报告智能体\")) {\n                // 先移动到末尾\n                const endPos = editorRef.current.state.doc.content.size;\n                editorRef.current.commands.setTextSelection(endPos);\n                // 然后插入空格\n                editorRef.current.commands.insertContent(\" \");\n              }\n              \n              // 定位光标到末尾并聚焦\n              const finalEndPos = editorRef.current.state.doc.content.size;\n              editorRef.current.commands.setTextSelection(finalEndPos);\n              editorRef.current.commands.focus();\n            }\n          }, 10);\n        }\n      },\n    }));\n\n    useEffect(() => {\n      handleEnterRef.current = onEnter;\n    }, [onEnter]);\n\n    const { provider, loading } = useRAGProvider();\n\n    const extensions = useMemo(() => {\n      const extensions = [\n        StarterKit,\n        Markdown.configure({\n          html: true,\n          tightLists: true,\n          tightListClass: \"tight\",\n          bulletListMarker: \"-\",\n          linkify: false,\n          breaks: false,\n          transformPastedText: false,\n          transformCopiedText: false,\n        }),\n        Placeholder.configure({\n          showOnlyCurrent: false,\n          placeholder: provider\n            ? \"What can I do for you? \\nYou may refer to RAG resources or agents by using @.\"\n            : \"What can I do for you?\\nYou can use @ to select agents or resources.\",\n          emptyEditorClass: \"placeholder\",\n        }),\n        Extension.create({\n          name: \"keyboardHandler\",\n          addKeyboardShortcuts() {\n            return {\n              Enter: () => {\n                if (handleEnterRef.current) {\n                  const { text, resources } = formatMessage(\n                    this.editor.getJSON() ?? [],\n                  );\n                  handleEnterRef.current(text, resources);\n                }\n                return this.editor.commands.clearContent();\n              },\n            };\n          },\n        }),\n        // 智能体mention\n        Mention.configure({\n          HTMLAttributes: {\n            class: \"mention agent-mention\",\n          },\n          suggestion: agentSuggestion,\n        }) as Extension,\n      ];\n      \n      // RAG资源mention\n      if (provider) {\n        extensions.push(\n          Mention.configure({\n            HTMLAttributes: {\n              class: \"mention resource-mention\",\n            },\n            suggestion: resourceSuggestion,\n          }) as Extension,\n        );\n      }\n      return extensions;\n    }, [provider]);\n\n    if (loading) {\n      return (\n        <div className={className}>\n          <LoadingOutlined />\n        </div>\n      );\n    }\n\n    return (\n      <div className={className}>\n        <EditorRoot>\n          <EditorContent\n            immediatelyRender={false}\n            extensions={extensions}\n            className=\"border-muted h-full w-full overflow-auto\"\n            editorProps={{\n              attributes: {\n                class:\n                  \"prose prose-base dark:prose-invert inline-editor font-default focus:outline-none max-w-full\",\n              },\n              transformPastedHTML: transformPastedHTML,\n            }}\n            onCreate={({ editor }) => {\n              editorRef.current = editor;\n            }}\n            onUpdate={({ editor }) => {\n              debouncedUpdates(editor);\n            }}\n          ></EditorContent>\n        </EditorRoot>\n      </div>\n    );\n  },\n);\n\nfunction transformPastedHTML(html: string) {\n  try {\n    // Strip HTML from user-pasted content\n    const tempEl = document.createElement(\"div\");\n    tempEl.innerHTML = html;\n\n    return tempEl.textContent || tempEl.innerText || \"\";\n  } catch (error) {\n    console.error(\"Error transforming pasted HTML\", error);\n\n    return \"\";\n  }\n}\n\nexport default MessageInput;\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AACA;AACA;AAAA;AAQA;AACA;AAGA;AACA;AACA;AAEA;AACA;;;AArBA;;;;;;;;;;;;AAoCA,SAAS,cAAc,OAAoB;IACzC,IAAI,QAAQ,OAAO,EAAE;QACnB,MAAM,SAGF;YACF,MAAM;YACN,WAAW,EAAE;QACf;QACA,KAAK,MAAM,QAAQ,QAAQ,OAAO,CAAE;YAClC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,cAAc;YAC1C,OAAO,IAAI,IAAI;YACf,OAAO,SAAS,CAAC,IAAI,IAAI;QAC3B;QACA,OAAO;IACT,OAAO;QACL,OAAO,WAAW;IACpB;AACF;AAEA,SAAS,WAAW,IAAiB;IAInC,IAAI,KAAK,IAAI,KAAK,QAAQ;QACxB,OAAO;YAAE,MAAM,KAAK,IAAI,IAAI;YAAI,WAAW,EAAE;QAAC;IAChD;IACA,IAAI,KAAK,IAAI,KAAK,WAAW;QAC3B,kBAAkB;QAClB,IAAI,KAAK,KAAK,EAAE,IAAI,SAAS,WAAW;YACtC,OAAO;gBACL,MAAM,CAAC,CAAC,EAAE,KAAK,KAAK,EAAE,MAAM,CAAC,CAAC;gBAC9B,WAAW,EAAE;YACf;QACF;QACA,eAAe;QACf,OAAO;YACL,MAAM,CAAC,CAAC,EAAE,KAAK,KAAK,EAAE,MAAM,EAAE,EAAE,KAAK,KAAK,EAAE,GAAG,CAAC,CAAC;YACjD,WAAW;gBACT;oBAAE,KAAK,KAAK,KAAK,EAAE,MAAM;oBAAI,OAAO,KAAK,KAAK,EAAE,SAAS;gBAAG;aAC7D;QACH;IACF;IACA,OAAO;QAAE,MAAM;QAAI,WAAW,EAAE;IAAC;AACnC;AAEA,MAAM,6BAAe,GAAA,CAAA,GAAA,sQAAA,CAAA,aAAU,AAAD,UAC5B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAqB,EAAE;;IACpD,MAAM,YAAY,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAU;IACjC,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAE1B;IACF,MAAM,mBAAmB,CAAA,GAAA,gPAAA,CAAA,uBAAoB,AAAD;+DAC1C,OAAO;YACL,IAAI,UAAU;gBACZ,oDAAoD;gBACpD,MAAM,EAAE,IAAI,EAAE,GAAG,cAAc,OAAO,OAAO,MAAM,EAAE;gBACrD,SAAS;YACX;QACF;8DACA;IAGF,sQAAA,CAAA,UAAK,CAAC,mBAAmB,CAAC;4CAAK,IAAM,CAAC;gBACpC,KAAK;wDAAE;wBACL,UAAU,OAAO,EAAE,KAAK;oBAC1B;;gBACA,MAAM;wDAAE;wBACN,IAAI,SAAS;4BACX,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,cAC1B,UAAU,OAAO,EAAE,aAAa,EAAE;4BAEpC,QAAQ,MAAM;wBAChB;wBACA,UAAU,OAAO,EAAE,SAAS;oBAC9B;;gBACA,UAAU;wDAAE,CAAC;wBACX,IAAI,UAAU,OAAO,EAAE;4BACrB,UAAU,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;4BACtC,oBAAoB;4BACpB;oEAAW;oCACT,IAAI,UAAU,OAAO,EAAE;wCACrB,6BAA6B;wCAC7B,IAAI,QAAQ,QAAQ,CAAC,aAAa,QAAQ,QAAQ,CAAC,WAAW;4CAC5D,SAAS;4CACT,MAAM,SAAS,UAAU,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI;4CACvD,UAAU,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC;4CAC5C,SAAS;4CACT,UAAU,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC;wCAC3C;wCAEA,aAAa;wCACb,MAAM,cAAc,UAAU,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI;wCAC5D,UAAU,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC;wCAC5C,UAAU,OAAO,CAAC,QAAQ,CAAC,KAAK;oCAClC;gCACF;mEAAG;wBACL;oBACF;;YACF,CAAC;;IAED,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;kCAAE;YACR,eAAe,OAAO,GAAG;QAC3B;iCAAG;QAAC;KAAQ;IAEZ,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE3C,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;4CAAE;YACzB,MAAM,aAAa;gBACjB,+QAAA,CAAA,aAAU;gBACV,kRAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;oBACjB,MAAM;oBACN,YAAY;oBACZ,gBAAgB;oBAChB,kBAAkB;oBAClB,SAAS;oBACT,QAAQ;oBACR,qBAAqB;oBACrB,qBAAqB;gBACvB;gBACA,kQAAA,CAAA,cAAW,CAAC,SAAS,CAAC;oBACpB,iBAAiB;oBACjB,aAAa,WACT,kFACA;oBACJ,kBAAkB;gBACpB;gBACA,mPAAA,CAAA,YAAS,CAAC,MAAM,CAAC;oBACf,MAAM;oBACN;wBACE,OAAO;4BACL,KAAK;oEAAE;oCACL,IAAI,eAAe,OAAO,EAAE;wCAC1B,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,cAC1B,IAAI,CAAC,MAAM,CAAC,OAAO,MAAM,EAAE;wCAE7B,eAAe,OAAO,CAAC,MAAM;oCAC/B;oCACA,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY;gCAC1C;;wBACF;oBACF;gBACF;gBACA,aAAa;gBACb,wQAAA,CAAA,UAAO,CAAC,SAAS,CAAC;oBAChB,gBAAgB;wBACd,OAAO;oBACT;oBACA,YAAY,4JAAA,CAAA,kBAAe;gBAC7B;aACD;YAED,eAAe;YACf,IAAI,UAAU;gBACZ,WAAW,IAAI,CACb,wQAAA,CAAA,UAAO,CAAC,SAAS,CAAC;oBAChB,gBAAgB;wBACd,OAAO;oBACT;oBACA,YAAY,+JAAA,CAAA,qBAAkB;gBAChC;YAEJ;YACA,OAAO;QACT;2CAAG;QAAC;KAAS;IAEb,IAAI,SAAS;QACX,qBACE,sSAAC;YAAI,WAAW;sBACd,cAAA,sSAAC,qUAAA,CAAA,kBAAe;;;;;;;;;;IAGtB;IAEA,qBACE,sSAAC;QAAI,WAAW;kBACd,cAAA,sSAAC,kQAAA,CAAA,aAAU;sBACT,cAAA,sSAAC,kQAAA,CAAA,gBAAa;gBACZ,mBAAmB;gBACnB,YAAY;gBACZ,WAAU;gBACV,aAAa;oBACX,YAAY;wBACV,OACE;oBACJ;oBACA,qBAAqB;gBACvB;gBACA,UAAU,CAAC,EAAE,MAAM,EAAE;oBACnB,UAAU,OAAO,GAAG;gBACtB;gBACA,UAAU,CAAC,EAAE,MAAM,EAAE;oBACnB,iBAAiB;gBACnB;;;;;;;;;;;;;;;;AAKV;;QAlJ2B,gPAAA,CAAA,uBAAoB;QAqDf,8HAAA,CAAA,iBAAc;;;;QArDnB,gPAAA,CAAA,uBAAoB;QAqDf,8HAAA,CAAA,iBAAc;;;;AAgGhD,SAAS,oBAAoB,IAAY;IACvC,IAAI;QACF,sCAAsC;QACtC,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,SAAS,GAAG;QAEnB,OAAO,OAAO,WAAW,IAAI,OAAO,SAAS,IAAI;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,OAAO;IACT;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1181, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/deer-flow/report-style-dialog.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { useState } from \"react\";\r\nimport { Check, FileText, Newspaper, Users, GraduationCap } from \"lucide-react\";\r\n\r\nimport { <PERSON><PERSON> } from \"~/components/ui/button\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from \"~/components/ui/dialog\";\r\nimport { setReportStyle, useSettingsStore } from \"~/core/store\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport { Tooltip } from \"./tooltip\";\r\n\r\nconst REPORT_STYLES = [\r\n  {\r\n    value: \"academic\" as const,\r\n    label: \"Academic\",\r\n    description: \"Formal, objective, and analytical with precise terminology\",\r\n    icon: GraduationCap,\r\n  },\r\n  {\r\n    value: \"popular_science\" as const,\r\n    label: \"Popular Science\",\r\n    description: \"Engaging and accessible for general audience\",\r\n    icon: FileText,\r\n  },\r\n  {\r\n    value: \"news\" as const,\r\n    label: \"News\",\r\n    description: \"Factual, concise, and impartial journalistic style\",\r\n    icon: Newspaper,\r\n  },\r\n  {\r\n    value: \"social_media\" as const,\r\n    label: \"Social Media\",\r\n    description: \"Concise, attention-grabbing, and shareable\",\r\n    icon: Users,\r\n  },\r\n];\r\n\r\nexport function ReportStyleDialog() {\r\n  const [open, setOpen] = useState(false);\r\n  const currentStyle = useSettingsStore((state) => state.general.reportStyle);\r\n\r\n  const handleStyleChange = (\r\n    style: \"academic\" | \"popular_science\" | \"news\" | \"social_media\",\r\n  ) => {\r\n    setReportStyle(style);\r\n    setOpen(false);\r\n  };\r\n\r\n  const currentStyleConfig =\r\n    REPORT_STYLES.find((style) => style.value === currentStyle) ||\r\n    REPORT_STYLES[0]!;\r\n  const CurrentIcon = currentStyleConfig.icon;\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={setOpen}>\r\n      <Tooltip\r\n        className=\"max-w-60\"\r\n        title={\r\n          <div>\r\n            <h3 className=\"mb-2 font-bold\">\r\n              Writing Style: {currentStyleConfig.label}\r\n            </h3>\r\n            <p>\r\n              Choose the writing style for your research reports. Different\r\n              styles are optimized for different audiences and purposes.\r\n            </p>\r\n          </div>\r\n        }\r\n      >\r\n        <DialogTrigger asChild>\r\n          <Button\r\n            className=\"!border-brand !text-brand rounded-2xl\"\r\n            variant=\"outline\"\r\n          >\r\n            <CurrentIcon className=\"h-4 w-4\" /> {currentStyleConfig.label}\r\n          </Button>\r\n        </DialogTrigger>\r\n      </Tooltip>\r\n      <DialogContent className=\"sm:max-w-[500px]\">\r\n        <DialogHeader>\r\n          <DialogTitle>Choose Writing Style</DialogTitle>\r\n          <DialogDescription>\r\n            Select the writing style for your research reports. Each style is\r\n            optimized for different audiences and purposes.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n        <div className=\"grid gap-3 py-4\">\r\n          {REPORT_STYLES.map((style) => {\r\n            const Icon = style.icon;\r\n            const isSelected = currentStyle === style.value;\r\n\r\n            return (\r\n              <button\r\n                key={style.value}\r\n                className={cn(\r\n                  \"hover:bg-accent flex items-start gap-3 rounded-lg border p-4 text-left transition-colors\",\r\n                  isSelected && \"border-primary bg-accent\",\r\n                )}\r\n                onClick={() => handleStyleChange(style.value)}\r\n              >\r\n                <Icon className=\"mt-0.5 h-5 w-5 shrink-0\" />\r\n                <div className=\"flex-1 space-y-1\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <h4 className=\"font-medium\">{style.label}</h4>\r\n                    {isSelected && <Check className=\"text-primary h-4 w-4\" />}\r\n                  </div>\r\n                  <p className=\"text-muted-foreground text-sm\">\r\n                    {style.description}\r\n                  </p>\r\n                </div>\r\n              </button>\r\n            );\r\n          })}\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAQA;AAAA;AACA;AAEA;;;;;;;;;;AAEA,MAAM,gBAAgB;IACpB;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,+SAAA,CAAA,gBAAa;IACrB;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,qSAAA,CAAA,WAAQ;IAChB;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,mSAAA,CAAA,YAAS;IACjB;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,2RAAA,CAAA,QAAK;IACb;CACD;AAEM,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,eAAe,CAAA,GAAA,4IAAA,CAAA,mBAAgB,AAAD;4DAAE,CAAC,QAAU,MAAM,OAAO,CAAC,WAAW;;IAE1E,MAAM,oBAAoB,CACxB;QAEA,CAAA,GAAA,4IAAA,CAAA,iBAAc,AAAD,EAAE;QACf,QAAQ;IACV;IAEA,MAAM,qBACJ,cAAc,IAAI,CAAC,CAAC,QAAU,MAAM,KAAK,KAAK,iBAC9C,aAAa,CAAC,EAAE;IAClB,MAAM,cAAc,mBAAmB,IAAI;IAE3C,qBACE,sSAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;;0BAChC,sSAAC,gJAAA,CAAA,UAAO;gBACN,WAAU;gBACV,qBACE,sSAAC;;sCACC,sSAAC;4BAAG,WAAU;;gCAAiB;gCACb,mBAAmB,KAAK;;;;;;;sCAE1C,sSAAC;sCAAE;;;;;;;;;;;;0BAOP,cAAA,sSAAC,qIAAA,CAAA,gBAAa;oBAAC,OAAO;8BACpB,cAAA,sSAAC,qIAAA,CAAA,SAAM;wBACL,WAAU;wBACV,SAAQ;;0CAER,sSAAC;gCAAY,WAAU;;;;;;4BAAY;4BAAE,mBAAmB,KAAK;;;;;;;;;;;;;;;;;0BAInE,sSAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,sSAAC,qIAAA,CAAA,eAAY;;0CACX,sSAAC,qIAAA,CAAA,cAAW;0CAAC;;;;;;0CACb,sSAAC,qIAAA,CAAA,oBAAiB;0CAAC;;;;;;;;;;;;kCAKrB,sSAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC;4BAClB,MAAM,OAAO,MAAM,IAAI;4BACvB,MAAM,aAAa,iBAAiB,MAAM,KAAK;4BAE/C,qBACE,sSAAC;gCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4FACA,cAAc;gCAEhB,SAAS,IAAM,kBAAkB,MAAM,KAAK;;kDAE5C,sSAAC;wCAAK,WAAU;;;;;;kDAChB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAG,WAAU;kEAAe,MAAM,KAAK;;;;;;oDACvC,4BAAc,sSAAC,2RAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;0DAElC,sSAAC;gDAAE,WAAU;0DACV,MAAM,WAAW;;;;;;;;;;;;;+BAdjB,MAAM,KAAK;;;;;wBAmBtB;;;;;;;;;;;;;;;;;;AAKV;GAhFgB;;QAEO,4IAAA,CAAA,mBAAgB;;;KAFvB", "debugId": null}}, {"offset": {"line": 1433, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/magicui/border-beam.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\nimport { motion, type MotionStyle, type Transition } from \"motion/react\";\r\n\r\ninterface BorderBeamProps {\r\n  /**\r\n   * The size of the border beam.\r\n   */\r\n  size?: number;\r\n  /**\r\n   * The duration of the border beam.\r\n   */\r\n  duration?: number;\r\n  /**\r\n   * The delay of the border beam.\r\n   */\r\n  delay?: number;\r\n  /**\r\n   * The color of the border beam from.\r\n   */\r\n  colorFrom?: string;\r\n  /**\r\n   * The color of the border beam to.\r\n   */\r\n  colorTo?: string;\r\n  /**\r\n   * The motion transition of the border beam.\r\n   */\r\n  transition?: Transition;\r\n  /**\r\n   * The class name of the border beam.\r\n   */\r\n  className?: string;\r\n  /**\r\n   * The style of the border beam.\r\n   */\r\n  style?: React.CSSProperties;\r\n  /**\r\n   * Whether to reverse the animation direction.\r\n   */\r\n  reverse?: boolean;\r\n  /**\r\n   * The initial offset position (0-100).\r\n   */\r\n  initialOffset?: number;\r\n}\r\n\r\nexport const BorderBeam = ({\r\n  className,\r\n  size = 50,\r\n  delay = 0,\r\n  duration = 6,\r\n  colorFrom = \"#ffaa40\",\r\n  colorTo = \"#9c40ff\",\r\n  transition,\r\n  style,\r\n  reverse = false,\r\n  initialOffset = 0,\r\n}: BorderBeamProps) => {\r\n  return (\r\n    <div className=\"pointer-events-none absolute inset-0 rounded-[inherit] border border-transparent [mask-image:linear-gradient(transparent,transparent),linear-gradient(#000,#000)] [mask-composite:intersect] [mask-clip:padding-box,border-box]\">\r\n      <motion.div\r\n        className={cn(\r\n          \"absolute aspect-square\",\r\n          \"bg-gradient-to-l from-[var(--color-from)] via-[var(--color-to)] to-transparent\",\r\n          className,\r\n        )}\r\n        style={\r\n          {\r\n            width: size,\r\n            offsetPath: `rect(0 auto auto 0 round ${size}px)`,\r\n            \"--color-from\": colorFrom,\r\n            \"--color-to\": colorTo,\r\n            ...style,\r\n          } as MotionStyle\r\n        }\r\n        initial={{ offsetDistance: `${initialOffset}%` }}\r\n        animate={{\r\n          offsetDistance: reverse\r\n            ? [`${100 - initialOffset}%`, `${-initialOffset}%`]\r\n            : [`${initialOffset}%`, `${100 + initialOffset}%`],\r\n        }}\r\n        transition={{\r\n          repeat: Infinity,\r\n          ease: \"linear\",\r\n          duration,\r\n          delay: -delay,\r\n          ...transition,\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAgDO,MAAM,aAAa,CAAC,EACzB,SAAS,EACT,OAAO,EAAE,EACT,QAAQ,CAAC,EACT,WAAW,CAAC,EACZ,YAAY,SAAS,EACrB,UAAU,SAAS,EACnB,UAAU,EACV,KAAK,EACL,UAAU,KAAK,EACf,gBAAgB,CAAC,EACD;IAChB,qBACE,sSAAC;QAAI,WAAU;kBACb,cAAA,sSAAC,sVAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0BACA,kFACA;YAEF,OACE;gBACE,OAAO;gBACP,YAAY,CAAC,yBAAyB,EAAE,KAAK,GAAG,CAAC;gBACjD,gBAAgB;gBAChB,cAAc;gBACd,GAAG,KAAK;YACV;YAEF,SAAS;gBAAE,gBAAgB,GAAG,cAAc,CAAC,CAAC;YAAC;YAC/C,SAAS;gBACP,gBAAgB,UACZ;oBAAC,GAAG,MAAM,cAAc,CAAC,CAAC;oBAAE,GAAG,CAAC,cAAc,CAAC,CAAC;iBAAC,GACjD;oBAAC,GAAG,cAAc,CAAC,CAAC;oBAAE,GAAG,MAAM,cAAc,CAAC,CAAC;iBAAC;YACtD;YACA,YAAY;gBACV,QAAQ;gBACR,MAAM;gBACN;gBACA,OAAO,CAAC;gBACR,GAAG,UAAU;YACf;;;;;;;;;;;AAIR;KA7Ca", "debugId": null}}, {"offset": {"line": 1497, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/app/chat/components/input-box.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\n// SPDX-License-Identifier: MIT\n\nimport { MagicWandIcon } from \"@radix-ui/react-icons\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { ArrowUp, Lightbulb, X } from \"lucide-react\";\nimport { useCallback, useEffect, useMemo, useRef, useState } from \"react\";\n\nimport { Detective } from \"~/components/deer-flow/icons/detective\";\nimport MessageInput, {\n  type MessageInputRef,\n} from \"~/components/deer-flow/message-input\";\nimport { ReportStyleDialog } from \"~/components/deer-flow/report-style-dialog\";\nimport { Tooltip } from \"~/components/deer-flow/tooltip\";\nimport { BorderBeam } from \"~/components/magicui/border-beam\";\nimport { Button } from \"~/components/ui/button\";\nimport { enhancePrompt } from \"~/core/api\";\nimport { getConfig } from \"~/core/api/config\";\nimport type { Option, Resource } from \"~/core/messages\";\nimport {\n  setEnableDeepThinking,\n  setEnableBackgroundInvestigation,\n  useSettingsStore,\n  getAgentMention,\n} from \"~/core/store\";\nimport { cn } from \"~/lib/utils\";\n\nexport function InputBox({\n  className,\n  responding,\n  feedback,\n  onSend,\n  onCancel,\n  onRemoveFeedback,\n}: {\n  className?: string;\n  size?: \"large\" | \"normal\";\n  responding?: boolean;\n  feedback?: { option: Option } | null;\n  onSend?: (\n    message: string,\n    options?: {\n      interruptFeedback?: string;\n      resources?: Array<Resource>;\n    },\n  ) => void;\n  onCancel?: () => void;\n  onRemoveFeedback?: () => void;\n}) {\n  const enableDeepThinking = useSettingsStore(\n    (state) => state.general.enableDeepThinking,\n  );\n  const backgroundInvestigation = useSettingsStore(\n    (state) => state.general.enableBackgroundInvestigation,\n  );\n  const reasoningModel = useMemo(() => getConfig().models.reasoning?.[0], []);\n  const reportStyle = useSettingsStore((state) => state.general.reportStyle);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<MessageInputRef>(null);\n  const feedbackRef = useRef<HTMLDivElement>(null);\n\n  // Enhancement state\n  const [isEnhancing, setIsEnhancing] = useState(false);\n  const [isEnhanceAnimating, setIsEnhanceAnimating] = useState(false);\n  const [currentPrompt, setCurrentPrompt] = useState(\"\");\n  const [isInitialized, setIsInitialized] = useState(false);\n\n\n\n  const handleSendMessage = useCallback(\n    (message: string, resources: Array<Resource>) => {\n      if (responding) {\n        onCancel?.();\n      } else {\n        if (message.trim() === \"\") {\n          return;\n        }\n        if (onSend) {\n          onSend(message, {\n            interruptFeedback: feedback?.option.value,\n            resources,\n          });\n          onRemoveFeedback?.();\n          // Clear enhancement animation after sending\n          setIsEnhanceAnimating(false);\n          \n          // 发送消息后，重新设置输入框内容为当前智能体的mention\n          setTimeout(() => {\n            const agentMention = getAgentMention();\n            if (agentMention && inputRef.current) {\n              inputRef.current.setContent(agentMention);\n            }\n          }, 100);\n        }\n      }\n    },\n    [responding, onCancel, onSend, feedback, onRemoveFeedback],\n  );\n\n  const handleEnhancePrompt = useCallback(async () => {\n    if (currentPrompt.trim() === \"\" || isEnhancing) {\n      return;\n    }\n\n    setIsEnhancing(true);\n    setIsEnhanceAnimating(true);\n\n    try {\n      const enhancedPrompt = await enhancePrompt({\n        prompt: currentPrompt,\n        report_style: reportStyle.toUpperCase(),\n      });\n\n      // Add a small delay for better UX\n      await new Promise((resolve) => setTimeout(resolve, 500));\n\n      // Update the input with the enhanced prompt with animation\n      if (inputRef.current) {\n        inputRef.current.setContent(enhancedPrompt);\n        setCurrentPrompt(enhancedPrompt);\n      }\n\n      // Keep animation for a bit longer to show the effect\n      setTimeout(() => {\n        setIsEnhanceAnimating(false);\n      }, 1000);\n    } catch (error) {\n      console.error(\"Failed to enhance prompt:\", error);\n      setIsEnhanceAnimating(false);\n      // Could add toast notification here\n    } finally {\n      setIsEnhancing(false);\n    }\n  }, [currentPrompt, isEnhancing, reportStyle]);\n\n  // 初始化时预填充当前智能体\n  useEffect(() => {\n    if (!isInitialized && inputRef.current) {\n      const agentMention = getAgentMention();\n      if (agentMention) {\n        inputRef.current.setContent(agentMention);\n      }\n      setIsInitialized(true);\n    }\n  }, [isInitialized]);\n\n  return (\n    <div\n      className={cn(\n        \"bg-card relative flex h-full w-full flex-col rounded-[24px] border\",\n        className,\n      )}\n      ref={containerRef}\n    >\n      <div className=\"w-full\">\n        <AnimatePresence>\n          {feedback && (\n            <motion.div\n              ref={feedbackRef}\n              className=\"bg-background border-brand absolute top-0 left-0 mt-2 ml-4 flex items-center justify-center gap-1 rounded-2xl border px-2 py-0.5\"\n              initial={{ opacity: 0, scale: 0 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 0 }}\n              transition={{ duration: 0.2, ease: \"easeInOut\" }}\n            >\n              <div className=\"text-brand flex h-full w-full items-center justify-center text-sm opacity-90\">\n                {feedback.option.text}\n              </div>\n              <X\n                className=\"cursor-pointer opacity-60\"\n                size={16}\n                onClick={onRemoveFeedback}\n              />\n            </motion.div>\n          )}\n          {isEnhanceAnimating && (\n            <motion.div\n              className=\"pointer-events-none absolute inset-0 z-20\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"relative h-full w-full\">\n                {/* Sparkle effect overlay */}\n                <motion.div\n                  className=\"absolute inset-0 rounded-[24px] bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10\"\n                  animate={{\n                    background: [\n                      \"linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1))\",\n                      \"linear-gradient(225deg, rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1))\",\n                      \"linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1))\",\n                    ],\n                  }}\n                  transition={{ duration: 2, repeat: Infinity }}\n                />\n                {/* Floating sparkles */}\n                {[...Array(6)].map((_, i) => (\n                  <motion.div\n                    key={i}\n                    className=\"absolute h-2 w-2 rounded-full bg-blue-400\"\n                    style={{\n                      left: `${20 + i * 12}%`,\n                      top: `${30 + (i % 2) * 40}%`,\n                    }}\n                    animate={{\n                      y: [-10, -20, -10],\n                      opacity: [0, 1, 0],\n                      scale: [0.5, 1, 0.5],\n                    }}\n                    transition={{\n                      duration: 1.5,\n                      repeat: Infinity,\n                      delay: i * 0.2,\n                    }}\n                  />\n                ))}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n        <MessageInput\n          className={cn(\n            \"h-24 px-4 pt-5\",\n            feedback && \"pt-9\",\n            isEnhanceAnimating && \"transition-all duration-500\",\n          )}\n          ref={inputRef}\n          onEnter={handleSendMessage}\n          onChange={setCurrentPrompt}\n        />\n      </div>\n      <div className=\"flex items-center px-4 py-2\">\n        <div className=\"flex grow gap-2\">\n          {reasoningModel && (\n            <Tooltip\n              className=\"max-w-60\"\n              title={\n                <div>\n                  <h3 className=\"mb-2 font-bold\">\n                    Deep Thinking Mode: {enableDeepThinking ? \"On\" : \"Off\"}\n                  </h3>\n                  <p>\n                    When enabled, DeerFlow will use reasoning model (\n                    {reasoningModel}) to generate more thoughtful plans.\n                  </p>\n                </div>\n              }\n            >\n              <Button\n                className={cn(\n                  \"rounded-2xl\",\n                  enableDeepThinking && \"!border-brand !text-brand\",\n                )}\n                variant=\"outline\"\n                onClick={() => {\n                  setEnableDeepThinking(!enableDeepThinking);\n                }}\n              >\n                <Lightbulb /> Deep Thinking\n              </Button>\n            </Tooltip>\n          )}\n\n          <Tooltip\n            className=\"max-w-60\"\n            title={\n              <div>\n                <h3 className=\"mb-2 font-bold\">\n                  Investigation Mode: {backgroundInvestigation ? \"On\" : \"Off\"}\n                </h3>\n                <p>\n                  When enabled, DeerFlow will perform a quick search before\n                  planning. This is useful for researches related to ongoing\n                  events and news.\n                </p>\n              </div>\n            }\n          >\n            <Button\n              className={cn(\n                \"rounded-2xl\",\n                backgroundInvestigation && \"!border-brand !text-brand\",\n              )}\n              variant=\"outline\"\n              onClick={() =>\n                setEnableBackgroundInvestigation(!backgroundInvestigation)\n              }\n            >\n              <Detective /> Investigation\n            </Button>\n          </Tooltip>\n          <ReportStyleDialog />\n        </div>\n        <div className=\"flex shrink-0 items-center gap-2\">\n          <Tooltip title=\"Enhance prompt with AI\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className={cn(\n                \"hover:bg-accent h-10 w-10\",\n                isEnhancing && \"animate-pulse\",\n              )}\n              onClick={handleEnhancePrompt}\n              disabled={isEnhancing || currentPrompt.trim() === \"\"}\n            >\n              {isEnhancing ? (\n                <div className=\"flex h-10 w-10 items-center justify-center\">\n                  <div className=\"bg-foreground h-3 w-3 animate-bounce rounded-full opacity-70\" />\n                </div>\n              ) : (\n                <MagicWandIcon className=\"text-brand\" />\n              )}\n            </Button>\n          </Tooltip>\n          <Tooltip title={responding ? \"Stop\" : \"Send\"}>\n            <Button\n              variant=\"outline\"\n              size=\"icon\"\n              className={cn(\"h-10 w-10 rounded-full\")}\n              onClick={() => inputRef.current?.submit()}\n            >\n              {responding ? (\n                <div className=\"flex h-10 w-10 items-center justify-center\">\n                  <div className=\"bg-foreground h-4 w-4 rounded-sm opacity-70\" />\n                </div>\n              ) : (\n                <ArrowUp />\n              )}\n            </Button>\n          </Tooltip>\n        </div>\n      </div>\n      {isEnhancing && (\n        <>\n          <BorderBeam\n            duration={5}\n            size={250}\n            className=\"from-transparent via-red-500 to-transparent\"\n          />\n          <BorderBeam\n            duration={5}\n            delay={3}\n            size={250}\n            className=\"from-transparent via-blue-500 to-transparent\"\n          />\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AAGA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AAAA;AAAA;AAMA;;;;;;;;;;;;;;;;;AAEO,SAAS,SAAS,EACvB,SAAS,EACT,UAAU,EACV,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,gBAAgB,EAejB;;IACC,MAAM,qBAAqB,CAAA,GAAA,4IAAA,CAAA,mBAAgB,AAAD;yDACxC,CAAC,QAAU,MAAM,OAAO,CAAC,kBAAkB;;IAE7C,MAAM,0BAA0B,CAAA,GAAA,4IAAA,CAAA,mBAAgB,AAAD;8DAC7C,CAAC,QAAU,MAAM,OAAO,CAAC,6BAA6B;;IAExD,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;4CAAE,IAAM,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE;2CAAE,EAAE;IAC1E,MAAM,cAAc,CAAA,GAAA,4IAAA,CAAA,mBAAgB,AAAD;kDAAE,CAAC,QAAU,MAAM,OAAO,CAAC,WAAW;;IACzE,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAmB;IACzC,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,oBAAoB;IACpB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAInD,MAAM,oBAAoB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAClC,CAAC,SAAiB;YAChB,IAAI,YAAY;gBACd;YACF,OAAO;gBACL,IAAI,QAAQ,IAAI,OAAO,IAAI;oBACzB;gBACF;gBACA,IAAI,QAAQ;oBACV,OAAO,SAAS;wBACd,mBAAmB,UAAU,OAAO;wBACpC;oBACF;oBACA;oBACA,4CAA4C;oBAC5C,sBAAsB;oBAEtB,gCAAgC;oBAChC;mEAAW;4BACT,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD;4BACnC,IAAI,gBAAgB,SAAS,OAAO,EAAE;gCACpC,SAAS,OAAO,CAAC,UAAU,CAAC;4BAC9B;wBACF;kEAAG;gBACL;YACF;QACF;kDACA;QAAC;QAAY;QAAU;QAAQ;QAAU;KAAiB;IAG5D,MAAM,sBAAsB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;qDAAE;YACtC,IAAI,cAAc,IAAI,OAAO,MAAM,aAAa;gBAC9C;YACF;YAEA,eAAe;YACf,sBAAsB;YAEtB,IAAI;gBACF,MAAM,iBAAiB,MAAM,CAAA,GAAA,2IAAA,CAAA,gBAAa,AAAD,EAAE;oBACzC,QAAQ;oBACR,cAAc,YAAY,WAAW;gBACvC;gBAEA,kCAAkC;gBAClC,MAAM,IAAI;iEAAQ,CAAC,UAAY,WAAW,SAAS;;gBAEnD,2DAA2D;gBAC3D,IAAI,SAAS,OAAO,EAAE;oBACpB,SAAS,OAAO,CAAC,UAAU,CAAC;oBAC5B,iBAAiB;gBACnB;gBAEA,qDAAqD;gBACrD;iEAAW;wBACT,sBAAsB;oBACxB;gEAAG;YACL,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,sBAAsB;YACtB,oCAAoC;YACtC,SAAU;gBACR,eAAe;YACjB;QACF;oDAAG;QAAC;QAAe;QAAa;KAAY;IAE5C,eAAe;IACf,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,CAAC,iBAAiB,SAAS,OAAO,EAAE;gBACtC,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD;gBACnC,IAAI,cAAc;oBAChB,SAAS,OAAO,CAAC,UAAU,CAAC;gBAC9B;gBACA,iBAAiB;YACnB;QACF;6BAAG;QAAC;KAAc;IAElB,qBACE,sSAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAEF,KAAK;;0BAEL,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,qSAAA,CAAA,kBAAe;;4BACb,0BACC,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;gCACT,KAAK;gCACL,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,MAAM;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAC7B,YAAY;oCAAE,UAAU;oCAAK,MAAM;gCAAY;;kDAE/C,sSAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,CAAC,IAAI;;;;;;kDAEvB,sSAAC,mRAAA,CAAA,IAAC;wCACA,WAAU;wCACV,MAAM;wCACN,SAAS;;;;;;;;;;;;4BAId,oCACC,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,MAAM;oCAAE,SAAS;gCAAE;gCACnB,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,sSAAC;oCAAI,WAAU;;sDAEb,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDACP,YAAY;oDACV;oDACA;oDACA;iDACD;4CACH;4CACA,YAAY;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;;;;;;wCAG7C;+CAAI,MAAM;yCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;gDAET,WAAU;gDACV,OAAO;oDACL,MAAM,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;oDACvB,KAAK,GAAG,KAAK,AAAC,IAAI,IAAK,GAAG,CAAC,CAAC;gDAC9B;gDACA,SAAS;oDACP,GAAG;wDAAC,CAAC;wDAAI,CAAC;wDAAI,CAAC;qDAAG;oDAClB,SAAS;wDAAC;wDAAG;wDAAG;qDAAE;oDAClB,OAAO;wDAAC;wDAAK;wDAAG;qDAAI;gDACtB;gDACA,YAAY;oDACV,UAAU;oDACV,QAAQ;oDACR,OAAO,IAAI;gDACb;+CAfK;;;;;;;;;;;;;;;;;;;;;;kCAsBjB,sSAAC,yJAAA,CAAA,UAAY;wBACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kBACA,YAAY,QACZ,sBAAsB;wBAExB,KAAK;wBACL,SAAS;wBACT,UAAU;;;;;;;;;;;;0BAGd,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;;4BACZ,gCACC,sSAAC,gJAAA,CAAA,UAAO;gCACN,WAAU;gCACV,qBACE,sSAAC;;sDACC,sSAAC;4CAAG,WAAU;;gDAAiB;gDACR,qBAAqB,OAAO;;;;;;;sDAEnD,sSAAC;;gDAAE;gDAEA;gDAAe;;;;;;;;;;;;;0CAKtB,cAAA,sSAAC,qIAAA,CAAA,SAAM;oCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,eACA,sBAAsB;oCAExB,SAAQ;oCACR,SAAS;wCACP,CAAA,GAAA,4IAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC;oCACzB;;sDAEA,sSAAC,mSAAA,CAAA,YAAS;;;;;wCAAG;;;;;;;;;;;;0CAKnB,sSAAC,gJAAA,CAAA,UAAO;gCACN,WAAU;gCACV,qBACE,sSAAC;;sDACC,sSAAC;4CAAG,WAAU;;gDAAiB;gDACR,0BAA0B,OAAO;;;;;;;sDAExD,sSAAC;sDAAE;;;;;;;;;;;;0CAQP,cAAA,sSAAC,qIAAA,CAAA,SAAM;oCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,eACA,2BAA2B;oCAE7B,SAAQ;oCACR,SAAS,IACP,CAAA,GAAA,4IAAA,CAAA,mCAAgC,AAAD,EAAE,CAAC;;sDAGpC,sSAAC,2JAAA,CAAA,YAAS;;;;;wCAAG;;;;;;;;;;;;0CAGjB,sSAAC,kKAAA,CAAA,oBAAiB;;;;;;;;;;;kCAEpB,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,gJAAA,CAAA,UAAO;gCAAC,OAAM;0CACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6BACA,eAAe;oCAEjB,SAAS;oCACT,UAAU,eAAe,cAAc,IAAI,OAAO;8CAEjD,4BACC,sSAAC;wCAAI,WAAU;kDACb,cAAA,sSAAC;4CAAI,WAAU;;;;;;;;;;6DAGjB,sSAAC,qRAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAI/B,sSAAC,gJAAA,CAAA,UAAO;gCAAC,OAAO,aAAa,SAAS;0CACpC,cAAA,sSAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;oCACd,SAAS,IAAM,SAAS,OAAO,EAAE;8CAEhC,2BACC,sSAAC;wCAAI,WAAU;kDACb,cAAA,sSAAC;4CAAI,WAAU;;;;;;;;;;6DAGjB,sSAAC,mSAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;YAMjB,6BACC;;kCACE,sSAAC,kJAAA,CAAA,aAAU;wBACT,UAAU;wBACV,MAAM;wBACN,WAAU;;;;;;kCAEZ,sSAAC,kJAAA,CAAA,aAAU;wBACT,UAAU;wBACV,OAAO;wBACP,MAAM;wBACN,WAAU;;;;;;;;;;;;;;AAMtB;GAnUgB;;QAsBa,4IAAA,CAAA,mBAAgB;QAGX,4IAAA,CAAA,mBAAgB;QAI5B,4IAAA,CAAA,mBAAgB;;;KA7BtB", "debugId": null}}, {"offset": {"line": 2064, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/deer-flow/loading-animation.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"bouncing-animation\": \"loading-animation-module__o3q3XG__bouncing-animation\",\n  \"loadingAnimation\": \"loading-animation-module__o3q3XG__loadingAnimation\",\n  \"sm\": \"loading-animation-module__o3q3XG__sm\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 2075, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/deer-flow/loading-animation.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport styles from \"./loading-animation.module.css\";\r\n\r\nexport function LoadingAnimation({\r\n  className,\r\n  size = \"normal\",\r\n}: {\r\n  className?: string;\r\n  size?: \"normal\" | \"sm\";\r\n}) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        styles.loadingAnimation,\r\n        size === \"sm\" && styles.sm,\r\n        className,\r\n      )}\r\n    >\r\n      <div></div>\r\n      <div></div>\r\n      <div></div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;;;;AAEO,SAAS,iBAAiB,EAC/B,SAAS,EACT,OAAO,QAAQ,EAIhB;IACC,qBACE,sSAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wKAAA,CAAA,UAAM,CAAC,gBAAgB,EACvB,SAAS,QAAQ,wKAAA,CAAA,UAAM,CAAC,EAAE,EAC1B;;0BAGF,sSAAC;;;;;0BACD,sSAAC;;;;;0BACD,sSAAC;;;;;;;;;;;AAGP;KApBgB", "debugId": null}}, {"offset": {"line": 2124, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/deer-flow/rolling-text.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport function RollingText({\r\n  className,\r\n  children,\r\n}: {\r\n  className?: string;\r\n  children?: string | string[];\r\n}) {\r\n  return (\r\n    <span\r\n      className={cn(\r\n        \"relative flex h-[2em] items-center overflow-hidden\",\r\n        className,\r\n      )}\r\n    >\r\n      <AnimatePresence mode=\"popLayout\">\r\n        <motion.div\r\n          className=\"absolute w-fit\"\r\n          style={{ transition: \"all 0.3s ease-in-out\" }}\r\n          initial={{ y: \"100%\", opacity: 0 }}\r\n          animate={{ y: \"0%\", opacity: 1 }}\r\n          exit={{ y: \"-100%\", opacity: 0 }}\r\n          transition={{ duration: 0.3, ease: \"easeInOut\" }}\r\n        >\r\n          {children}\r\n        </motion.div>\r\n      </AnimatePresence>\r\n    </span>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AAEA;;;;AAEO,SAAS,YAAY,EAC1B,SAAS,EACT,QAAQ,EAIT;IACC,qBACE,sSAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;kBAGF,cAAA,sSAAC,qSAAA,CAAA,kBAAe;YAAC,MAAK;sBACpB,cAAA,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,YAAY;gBAAuB;gBAC5C,SAAS;oBAAE,GAAG;oBAAQ,SAAS;gBAAE;gBACjC,SAAS;oBAAE,GAAG;oBAAM,SAAS;gBAAE;gBAC/B,MAAM;oBAAE,GAAG;oBAAS,SAAS;gBAAE;gBAC/B,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAY;0BAE9C;;;;;;;;;;;;;;;;AAKX;KA5BgB", "debugId": null}}, {"offset": {"line": 2191, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nfunction ScrollArea({\r\n  className,\r\n  children,\r\n  ref,\r\n  ...props\r\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\r\n  return (\r\n    <ScrollAreaPrimitive.Root\r\n      data-slot=\"scroll-area\"\r\n      className={cn(\"relative\", className)}\r\n      {...props}\r\n    >\r\n      <ScrollAreaPrimitive.Viewport\r\n        data-slot=\"scroll-area-viewport\"\r\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\r\n        // https://github.com/stackblitz-labs/use-stick-to-bottom/issues/15\r\n        ref={ref}\r\n      >\r\n        {children}\r\n      </ScrollAreaPrimitive.Viewport>\r\n      <ScrollBar />\r\n      <ScrollAreaPrimitive.Corner />\r\n    </ScrollAreaPrimitive.Root>\r\n  );\r\n}\r\n\r\nfunction ScrollBar({\r\n  className,\r\n  orientation = \"vertical\",\r\n  ...props\r\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\r\n  return (\r\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n      data-slot=\"scroll-area-scrollbar\"\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"flex touch-none p-px transition-colors select-none\",\r\n        orientation === \"vertical\" &&\r\n          \"h-full w-2.5 border-l border-l-transparent\",\r\n        orientation === \"horizontal\" &&\r\n          \"h-2.5 flex-col border-t border-t-transparent\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <ScrollAreaPrimitive.ScrollAreaThumb\r\n        data-slot=\"scroll-area-thumb\"\r\n        className=\"bg-border relative flex-1 rounded-full\"\r\n      />\r\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n  );\r\n}\r\n\r\nexport { ScrollArea, ScrollBar };\r\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,EACH,GAAG,OACmD;IACtD,qBACE,sSAAC,oRAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,sSAAC,oRAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;gBACV,mEAAmE;gBACnE,KAAK;0BAEJ;;;;;;0BAEH,sSAAC;;;;;0BACD,sSAAC,oRAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;KAxBS;AA0BT,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,sSAAC,oRAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,sSAAC,oRAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB;MAzBS", "debugId": null}}, {"offset": {"line": 2271, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/deer-flow/scroll-container.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport {\r\n  useEffect,\r\n  useImperativeHandle,\r\n  useLayoutEffect,\r\n  useRef,\r\n  type ReactNode,\r\n  type RefObject,\r\n} from \"react\";\r\nimport { useStickToBottom } from \"use-stick-to-bottom\";\r\n\r\nimport { ScrollArea } from \"~/components/ui/scroll-area\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport interface ScrollContainerProps {\r\n  className?: string;\r\n  children?: ReactNode;\r\n  scrollShadow?: boolean;\r\n  scrollShadowColor?: string;\r\n  autoScrollToBottom?: boolean;\r\n  ref?: RefObject<ScrollContainerRef | null>;\r\n}\r\n\r\nexport interface ScrollContainerRef {\r\n  scrollToBottom(): void;\r\n}\r\n\r\nexport function ScrollContainer({\r\n  className,\r\n  children,\r\n  scrollShadow = true,\r\n  scrollShadowColor = \"var(--background)\",\r\n  autoScrollToBottom = false,\r\n  ref,\r\n}: ScrollContainerProps) {\r\n  const { scrollRef, contentRef, scrollToBottom, isAtBottom } =\r\n    useStickToBottom({ initial: \"instant\" });\r\n  useImperativeHandle(ref, () => ({\r\n    scrollToBottom() {\r\n      if (isAtBottom) {\r\n        scrollToBottom();\r\n      }\r\n    },\r\n  }));\r\n\r\n  const tempScrollRef = useRef<HTMLElement>(null);\r\n  const tempContentRef = useRef<HTMLElement>(null);\r\n  useEffect(() => {\r\n    if (!autoScrollToBottom) {\r\n      tempScrollRef.current = scrollRef.current;\r\n      tempContentRef.current = contentRef.current;\r\n      scrollRef.current = null;\r\n      contentRef.current = null;\r\n    } else if (tempScrollRef.current && tempContentRef.current) {\r\n      scrollRef.current = tempScrollRef.current;\r\n      contentRef.current = tempContentRef.current;\r\n    }\r\n  }, [autoScrollToBottom, contentRef, scrollRef]);\r\n\r\n  return (\r\n    <div className={cn(\"relative\", className)}>\r\n      {scrollShadow && (\r\n        <>\r\n          <div\r\n            className={cn(\r\n              \"absolute top-0 right-0 left-0 z-10 h-10 bg-gradient-to-t\",\r\n              `from-transparent to-[var(--scroll-shadow-color)]`,\r\n            )}\r\n            style={\r\n              {\r\n                \"--scroll-shadow-color\": scrollShadowColor,\r\n              } as React.CSSProperties\r\n            }\r\n          ></div>\r\n          <div\r\n            className={cn(\r\n              \"absolute right-0 bottom-0 left-0 z-10 h-10 bg-gradient-to-b\",\r\n              `from-transparent to-[var(--scroll-shadow-color)]`,\r\n            )}\r\n            style={\r\n              {\r\n                \"--scroll-shadow-color\": scrollShadowColor,\r\n              } as React.CSSProperties\r\n            }\r\n          ></div>\r\n        </>\r\n      )}\r\n      <ScrollArea ref={scrollRef} className=\"h-full w-full\">\r\n        <div className=\"h-fit w-full\" ref={contentRef}>\r\n          {children}\r\n        </div>\r\n      </ScrollArea>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAQA;AAEA;AACA;;;;;;;AAeO,SAAS,gBAAgB,EAC9B,SAAS,EACT,QAAQ,EACR,eAAe,IAAI,EACnB,oBAAoB,mBAAmB,EACvC,qBAAqB,KAAK,EAC1B,GAAG,EACkB;;IACrB,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,GACzD,CAAA,GAAA,0QAAA,CAAA,mBAAgB,AAAD,EAAE;QAAE,SAAS;IAAU;IACxC,CAAA,GAAA,sQAAA,CAAA,sBAAmB,AAAD,EAAE;+CAAK,IAAM,CAAC;gBAC9B;oBACE,IAAI,YAAY;wBACd;oBACF;gBACF;YACF,CAAC;;IAED,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAe;IAC1C,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAe;IAC3C,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,oBAAoB;gBACvB,cAAc,OAAO,GAAG,UAAU,OAAO;gBACzC,eAAe,OAAO,GAAG,WAAW,OAAO;gBAC3C,UAAU,OAAO,GAAG;gBACpB,WAAW,OAAO,GAAG;YACvB,OAAO,IAAI,cAAc,OAAO,IAAI,eAAe,OAAO,EAAE;gBAC1D,UAAU,OAAO,GAAG,cAAc,OAAO;gBACzC,WAAW,OAAO,GAAG,eAAe,OAAO;YAC7C;QACF;oCAAG;QAAC;QAAoB;QAAY;KAAU;IAE9C,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;YAC5B,8BACC;;kCACE,sSAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA,CAAC,gDAAgD,CAAC;wBAEpD,OACE;4BACE,yBAAyB;wBAC3B;;;;;;kCAGJ,sSAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,CAAC,gDAAgD,CAAC;wBAEpD,OACE;4BACE,yBAAyB;wBAC3B;;;;;;;;0BAKR,sSAAC,6IAAA,CAAA,aAAU;gBAAC,KAAK;gBAAW,WAAU;0BACpC,cAAA,sSAAC;oBAAI,WAAU;oBAAe,KAAK;8BAChC;;;;;;;;;;;;;;;;;AAKX;GAnEgB;;QASZ,0QAAA,CAAA,mBAAgB;;;KATJ", "debugId": null}}, {"offset": {"line": 2388, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\r\n\r\nfunction Collapsible({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\r\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />\r\n}\r\n\r\nfunction CollapsibleTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleTrigger\r\n      data-slot=\"collapsible-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CollapsibleContent({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleContent\r\n      data-slot=\"collapsible-content\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,sSAAC,8QAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,sSAAC,8QAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,sSAAC,8QAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS", "debugId": null}}, {"offset": {"line": 2445, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,sSAAC,+QAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 2481, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/app/chat/components/data-analysis-card.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport {\r\n  Database,\r\n  AlertCircle,\r\n  CheckCircle,\r\n  Play,\r\n  Search,\r\n  Code,\r\n  Table,\r\n  BarChart3,\r\n  Zap,\r\n  Clock,\r\n  ChevronDown,\r\n  ChevronUp,\r\n  Copy,\r\n  Download\r\n} from \"lucide-react\";\r\nimport React, { useMemo, useState } from \"react\";\r\n\r\nimport { Markdown } from \"~/components/deer-flow/markdown\";\r\nimport { RainbowText } from \"~/components/deer-flow/rainbow-text\";\r\nimport { LoadingAnimation } from \"~/components/deer-flow/loading-animation\";\r\nimport { BorderBeam } from \"~/components/magicui/border-beam\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"~/components/ui/card\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Badge } from \"~/components/ui/badge\";\r\nimport { Separator } from \"~/components/ui/separator\";\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"~/components/ui/collapsible\";\r\nimport {\r\n  Table as UITable,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"~/components/ui/table\";\r\nimport { ScrollArea } from \"~/components/ui/scroll-area\";\r\nimport type { Message } from \"~/core/messages\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\ninterface TableData {\r\n  type: \"table\";\r\n  data: Record<string, any>[];\r\n  total: number;\r\n}\r\n\r\ninterface AnalysisStep {\r\n  type: \"keyword_extraction\" | \"schema_analysis\" | \"sql_generation\" | \"execution\" | \"result\";\r\n  title: string;\r\n  content: string;\r\n  status: \"pending\" | \"running\" | \"completed\" | \"error\";\r\n  timestamp?: string;\r\n  data?: any;\r\n}\r\n\r\nfunction DataAnalysisCard({\r\n  className,\r\n  message,\r\n}: {\r\n  className?: string;\r\n  message: Message;\r\n}) {\r\n  const [expandedSteps, setExpandedSteps] = useState<Set<number>>(new Set());\r\n  const [showRawData, setShowRawData] = useState(false);\r\n\r\n  // 解析消息内容，提取分析步骤和数据\r\n  const { analysisSteps, tableData, hasError, sqlQuery } = useMemo(() => {\r\n    const content = message.content ?? \"\";\r\n    const hasError = content.includes(\"[错误]\");\r\n\r\n    // 解析分析步骤\r\n    const steps: AnalysisStep[] = [];\r\n    const lines = content.split('\\n');\r\n\r\n    let currentStep: AnalysisStep | null = null;\r\n\r\n    for (const line of lines) {\r\n      if (line.includes('[日志]')) {\r\n        const logContent = line.replace('[日志]', '').trim();\r\n\r\n        if (logContent.includes('正在提取关键词')) {\r\n          currentStep = {\r\n            type: \"keyword_extraction\",\r\n            title: \"关键词提取\",\r\n            content: logContent,\r\n            status: message.isStreaming ? \"running\" : \"completed\"\r\n          };\r\n          steps.push(currentStep);\r\n        } else if (logContent.includes('正在获取数据库结构')) {\r\n          currentStep = {\r\n            type: \"schema_analysis\",\r\n            title: \"数据库结构分析\",\r\n            content: logContent,\r\n            status: message.isStreaming ? \"running\" : \"completed\"\r\n          };\r\n          steps.push(currentStep);\r\n        } else if (logContent.includes('正在生成SQL')) {\r\n          currentStep = {\r\n            type: \"sql_generation\",\r\n            title: \"SQL查询生成\",\r\n            content: logContent,\r\n            status: message.isStreaming ? \"running\" : \"completed\"\r\n          };\r\n          steps.push(currentStep);\r\n        } else if (logContent.includes('正在执行SQL')) {\r\n          currentStep = {\r\n            type: \"execution\",\r\n            title: \"查询执行\",\r\n            content: logContent,\r\n            status: message.isStreaming ? \"running\" : \"completed\"\r\n          };\r\n          steps.push(currentStep);\r\n        } else if (currentStep) {\r\n          currentStep.content += '\\n' + logContent;\r\n        }\r\n      } else if (line.includes('[错误]')) {\r\n        if (currentStep) {\r\n          currentStep.status = \"error\";\r\n          currentStep.content += '\\n' + line.replace('[错误]', '').trim();\r\n        }\r\n      }\r\n    }\r\n\r\n    // 提取SQL查询\r\n    const sqlMatch = content.match(/```sql\\n(.*?)\\n```/s);\r\n    const sqlQuery = sqlMatch?.[1] || null;\r\n\r\n    // 提取表格数据\r\n    const tableMatch = content.match(/\\[TABLE_DATA\\]\\s*(\\{.*?\\})/s);\r\n    let tableData: TableData | null = null;\r\n\r\n    if (tableMatch && tableMatch[1]) {\r\n      try {\r\n        tableData = JSON.parse(tableMatch[1]);\r\n        if (tableData && tableData.data) {\r\n          steps.push({\r\n            type: \"result\",\r\n            title: \"查询结果\",\r\n            content: `成功获取 ${tableData.total} 条记录`,\r\n            status: \"completed\",\r\n            data: tableData\r\n          });\r\n        }\r\n      } catch (e) {\r\n        console.error(\"Failed to parse table data:\", e);\r\n      }\r\n    }\r\n\r\n    return { analysisSteps: steps, tableData, hasError, sqlQuery };\r\n  }, [message.content, message.isStreaming]);\r\n\r\n  // 获取状态信息\r\n  const statusInfo = useMemo(() => {\r\n    if (hasError) {\r\n      return {\r\n        icon: AlertCircle,\r\n        color: \"text-red-500\",\r\n        bgColor: \"bg-red-50 dark:bg-red-950/20\",\r\n        borderColor: \"border-red-200 dark:border-red-800\",\r\n        status: \"错误\"\r\n      };\r\n    }\r\n\r\n    if (message.isStreaming) {\r\n      return {\r\n        icon: Play,\r\n        color: \"text-blue-500\",\r\n        bgColor: \"bg-blue-50 dark:bg-blue-950/20\",\r\n        borderColor: \"border-blue-200 dark:border-blue-800\",\r\n        status: \"分析中\"\r\n      };\r\n    }\r\n\r\n    return {\r\n      icon: CheckCircle,\r\n      color: \"text-green-500\",\r\n      bgColor: \"bg-green-50 dark:bg-green-950/20\",\r\n      borderColor: \"border-green-200 dark:border-green-800\",\r\n      status: \"完成\"\r\n    };\r\n  }, [hasError, message.isStreaming]);\r\n\r\n  const toggleStep = (index: number) => {\r\n    const newExpanded = new Set(expandedSteps);\r\n    if (newExpanded.has(index)) {\r\n      newExpanded.delete(index);\r\n    } else {\r\n      newExpanded.add(index);\r\n    }\r\n    setExpandedSteps(newExpanded);\r\n  };\r\n\r\n  const StatusIcon = statusInfo.icon;\r\n\r\n  return (\r\n    <motion.div\r\n      className={cn(\"w-full\", className)}\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.3, ease: \"easeOut\" }}\r\n    >\r\n      <Card className={cn(\r\n        \"w-full transition-all duration-200 relative overflow-hidden\",\r\n        statusInfo.borderColor,\r\n        statusInfo.bgColor\r\n      )}>\r\n        {/* 动态边框效果 */}\r\n        {message.isStreaming && (\r\n          <BorderBeam\r\n            size={60}\r\n            duration={4}\r\n            colorFrom=\"#3b82f6\"\r\n            colorTo=\"#8b5cf6\"\r\n          />\r\n        )}\r\n\r\n        <CardHeader className=\"pb-4\">\r\n          <CardTitle className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"relative\">\r\n                <Database className=\"h-6 w-6 text-primary\" />\r\n                {message.isStreaming && (\r\n                  <motion.div\r\n                    className=\"absolute -inset-1 rounded-full bg-blue-500/20\"\r\n                    animate={{ scale: [1, 1.2, 1] }}\r\n                    transition={{ duration: 2, repeat: Infinity }}\r\n                  />\r\n                )}\r\n              </div>\r\n              <div className=\"flex flex-col\">\r\n                <RainbowText\r\n                  className=\"text-lg font-semibold\"\r\n                  animated={message.isStreaming}\r\n                >\r\n                  数据分析智能体\r\n                </RainbowText>\r\n                <div className=\"flex items-center gap-2 mt-1\">\r\n                  <Badge\r\n                    variant={hasError ? \"destructive\" : message.isStreaming ? \"default\" : \"secondary\"}\r\n                    className=\"text-xs\"\r\n                  >\r\n                    <StatusIcon className=\"h-3 w-3 mr-1\" />\r\n                    {statusInfo.status}\r\n                  </Badge>\r\n                  {analysisSteps.length > 0 && (\r\n                    <Badge variant=\"outline\" className=\"text-xs\">\r\n                      {analysisSteps.filter(s => s.status === \"completed\").length}/{analysisSteps.length} 步骤\r\n                    </Badge>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* 操作按钮 */}\r\n            <div className=\"flex items-center gap-2\">\r\n              {sqlQuery && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={() => navigator.clipboard.writeText(sqlQuery)}\r\n                  className=\"h-8 w-8 p-0\"\r\n                >\r\n                  <Copy className=\"h-4 w-4\" />\r\n                </Button>\r\n              )}\r\n              {tableData && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={() => setShowRawData(!showRawData)}\r\n                  className=\"h-8 w-8 p-0\"\r\n                >\r\n                  <Code className=\"h-4 w-4\" />\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </CardTitle>\r\n        </CardHeader>\r\n\r\n        <CardContent className=\"space-y-6\">\r\n          {/* 分析步骤展示 */}\r\n          {analysisSteps.length > 0 && (\r\n            <div className=\"space-y-3\">\r\n              <div className=\"flex items-center gap-2 text-sm font-medium text-muted-foreground\">\r\n                <BarChart3 className=\"h-4 w-4\" />\r\n                分析流程\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                {analysisSteps.map((step, index) => (\r\n                  <AnalysisStepCard\r\n                    key={index}\r\n                    step={step}\r\n                    index={index}\r\n                    isExpanded={expandedSteps.has(index)}\r\n                    onToggle={() => toggleStep(index)}\r\n                    isStreaming={message.isStreaming && index === analysisSteps.length - 1}\r\n                  />\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* SQL查询展示 */}\r\n          {sqlQuery && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.2 }}\r\n              className=\"space-y-2\"\r\n            >\r\n              <div className=\"flex items-center gap-2 text-sm font-medium text-muted-foreground\">\r\n                <Code className=\"h-4 w-4\" />\r\n                生成的SQL查询\r\n              </div>\r\n              <div className=\"relative\">\r\n                <pre className=\"bg-muted/50 border rounded-lg p-4 text-sm overflow-x-auto\">\r\n                  <code className=\"language-sql\">{sqlQuery}</code>\r\n                </pre>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={() => navigator.clipboard.writeText(sqlQuery)}\r\n                  className=\"absolute top-2 right-2 h-8 w-8 p-0\"\r\n                >\r\n                  <Copy className=\"h-3 w-3\" />\r\n                </Button>\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n\r\n          {/* 表格数据展示 */}\r\n          {tableData && tableData.data && tableData.data.length > 0 && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.3 }}\r\n              className=\"space-y-3\"\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2 text-sm font-medium text-muted-foreground\">\r\n                  <Table className=\"h-4 w-4\" />\r\n                  查询结果\r\n                  <Badge variant=\"secondary\" className=\"text-xs\">\r\n                    {tableData.total} 条记录\r\n                  </Badge>\r\n                </div>\r\n\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={() => setShowRawData(!showRawData)}\r\n                    className=\"text-xs\"\r\n                  >\r\n                    {showRawData ? \"表格视图\" : \"原始数据\"}\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n\r\n              <AnimatePresence mode=\"wait\">\r\n                {showRawData ? (\r\n                  <motion.div\r\n                    key=\"raw\"\r\n                    initial={{ opacity: 0 }}\r\n                    animate={{ opacity: 1 }}\r\n                    exit={{ opacity: 0 }}\r\n                    className=\"bg-muted/30 border rounded-lg p-4\"\r\n                  >\r\n                    <ScrollArea className=\"h-64\">\r\n                      <pre className=\"text-xs font-mono\">\r\n                        {JSON.stringify(tableData.data, null, 2)}\r\n                      </pre>\r\n                    </ScrollArea>\r\n                  </motion.div>\r\n                ) : (\r\n                  <motion.div\r\n                    key=\"table\"\r\n                    initial={{ opacity: 0 }}\r\n                    animate={{ opacity: 1 }}\r\n                    exit={{ opacity: 0 }}\r\n                  >\r\n                    <DataTable data={tableData.data} />\r\n                  </motion.div>\r\n                )}\r\n              </AnimatePresence>\r\n            </motion.div>\r\n          )}\r\n\r\n          {/* 流式加载动画 */}\r\n          {message.isStreaming && (\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              className=\"flex items-center justify-center py-4\"\r\n            >\r\n              <LoadingAnimation className=\"scale-75\" />\r\n            </motion.div>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n    </motion.div>\r\n  );\r\n}\r\n\r\n// 分析步骤卡片组件\r\nfunction AnalysisStepCard({\r\n  step,\r\n  index,\r\n  isExpanded,\r\n  onToggle,\r\n  isStreaming\r\n}: {\r\n  step: AnalysisStep;\r\n  index: number;\r\n  isExpanded: boolean;\r\n  onToggle: () => void;\r\n  isStreaming?: boolean;\r\n}) {\r\n  const getStepIcon = (type: AnalysisStep['type']) => {\r\n    switch (type) {\r\n      case \"keyword_extraction\": return Search;\r\n      case \"schema_analysis\": return Database;\r\n      case \"sql_generation\": return Code;\r\n      case \"execution\": return Zap;\r\n      case \"result\": return Table;\r\n      default: return Clock;\r\n    }\r\n  };\r\n\r\n  const getStatusColor = (status: AnalysisStep['status']) => {\r\n    switch (status) {\r\n      case \"completed\": return \"text-green-500\";\r\n      case \"running\": return \"text-blue-500\";\r\n      case \"error\": return \"text-red-500\";\r\n      default: return \"text-gray-400\";\r\n    }\r\n  };\r\n\r\n  const StepIcon = getStepIcon(step.type);\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, x: -20 }}\r\n      animate={{ opacity: 1, x: 0 }}\r\n      transition={{ delay: index * 0.1 }}\r\n      className=\"border rounded-lg overflow-hidden\"\r\n    >\r\n      <Collapsible open={isExpanded} onOpenChange={onToggle}>\r\n        <CollapsibleTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            className=\"w-full justify-between p-4 h-auto hover:bg-muted/50\"\r\n          >\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"relative\">\r\n                <StepIcon className={cn(\"h-4 w-4\", getStatusColor(step.status))} />\r\n                {isStreaming && step.status === \"running\" && (\r\n                  <motion.div\r\n                    className=\"absolute -inset-1 rounded-full bg-blue-500/20\"\r\n                    animate={{ scale: [1, 1.3, 1] }}\r\n                    transition={{ duration: 1.5, repeat: Infinity }}\r\n                  />\r\n                )}\r\n              </div>\r\n              <div className=\"flex flex-col items-start\">\r\n                <span className=\"font-medium text-sm\">{step.title}</span>\r\n                <span className=\"text-xs text-muted-foreground\">\r\n                  {step.status === \"running\" ? \"执行中...\" :\r\n                   step.status === \"completed\" ? \"已完成\" :\r\n                   step.status === \"error\" ? \"执行失败\" : \"等待中\"}\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              {step.status === \"completed\" && (\r\n                <CheckCircle className=\"h-4 w-4 text-green-500\" />\r\n              )}\r\n              {step.status === \"error\" && (\r\n                <AlertCircle className=\"h-4 w-4 text-red-500\" />\r\n              )}\r\n              {step.status === \"running\" && isStreaming && (\r\n                <motion.div\r\n                  animate={{ rotate: 360 }}\r\n                  transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\r\n                >\r\n                  <Play className=\"h-4 w-4 text-blue-500\" />\r\n                </motion.div>\r\n              )}\r\n              {isExpanded ? (\r\n                <ChevronUp className=\"h-4 w-4\" />\r\n              ) : (\r\n                <ChevronDown className=\"h-4 w-4\" />\r\n              )}\r\n            </div>\r\n          </Button>\r\n        </CollapsibleTrigger>\r\n\r\n        <CollapsibleContent>\r\n          <div className=\"px-4 pb-4 pt-0\">\r\n            <Separator className=\"mb-3\" />\r\n            <div className=\"text-sm text-muted-foreground whitespace-pre-wrap\">\r\n              {step.content}\r\n            </div>\r\n            {step.data && step.type === \"result\" && (\r\n              <div className=\"mt-3 p-3 bg-muted/30 rounded-md\">\r\n                <div className=\"text-xs font-medium text-muted-foreground mb-2\">\r\n                  数据预览\r\n                </div>\r\n                <div className=\"text-xs font-mono\">\r\n                  {JSON.stringify(step.data.data?.slice(0, 2), null, 2)}\r\n                  {step.data.data?.length > 2 && \"\\n...\"}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </CollapsibleContent>\r\n      </Collapsible>\r\n    </motion.div>\r\n  );\r\n}\r\n\r\n// 数据表格组件\r\nfunction DataTable({ data }: { data: Record<string, any>[] }) {\r\n  if (!data || data.length === 0) {\r\n    return (\r\n      <div className=\"text-center py-8 text-muted-foreground\">\r\n        暂无数据\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const columns = Object.keys(data[0]);\r\n  const displayData = data.slice(0, 10); // 只显示前10条\r\n\r\n  return (\r\n    <div className=\"border rounded-lg overflow-hidden\">\r\n      <ScrollArea className=\"h-64\">\r\n        <UITable>\r\n          <TableHeader>\r\n            <TableRow>\r\n              {columns.map((column) => (\r\n                <TableHead key={column} className=\"font-medium\">\r\n                  {column}\r\n                </TableHead>\r\n              ))}\r\n            </TableRow>\r\n          </TableHeader>\r\n          <TableBody>\r\n            {displayData.map((row, index) => (\r\n              <motion.tr\r\n                key={index}\r\n                initial={{ opacity: 0, y: 10 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: index * 0.05 }}\r\n                className=\"border-b\"\r\n              >\r\n                {columns.map((column) => (\r\n                  <TableCell key={column} className=\"text-sm\">\r\n                    {typeof row[column] === 'object'\r\n                      ? JSON.stringify(row[column])\r\n                      : String(row[column] ?? '')\r\n                    }\r\n                  </TableCell>\r\n                ))}\r\n              </motion.tr>\r\n            ))}\r\n          </TableBody>\r\n        </UITable>\r\n      </ScrollArea>\r\n\r\n      {data.length > 10 && (\r\n        <div className=\"p-3 bg-muted/30 text-center text-xs text-muted-foreground\">\r\n          显示前 10 条，共 {data.length} 条记录\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport { DataAnalysisCard };"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAGA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;;;;;;AAaA;AAEA;;;;;;;;;;;;;;;;;AAiBA,SAAS,iBAAiB,EACxB,SAAS,EACT,OAAO,EAIR;;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,mBAAmB;IACnB,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;oCAAE;YAC/D,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,WAAW,QAAQ,QAAQ,CAAC;YAElC,SAAS;YACT,MAAM,QAAwB,EAAE;YAChC,MAAM,QAAQ,QAAQ,KAAK,CAAC;YAE5B,IAAI,cAAmC;YAEvC,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,QAAQ,CAAC,SAAS;oBACzB,MAAM,aAAa,KAAK,OAAO,CAAC,QAAQ,IAAI,IAAI;oBAEhD,IAAI,WAAW,QAAQ,CAAC,YAAY;wBAClC,cAAc;4BACZ,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,QAAQ,QAAQ,WAAW,GAAG,YAAY;wBAC5C;wBACA,MAAM,IAAI,CAAC;oBACb,OAAO,IAAI,WAAW,QAAQ,CAAC,cAAc;wBAC3C,cAAc;4BACZ,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,QAAQ,QAAQ,WAAW,GAAG,YAAY;wBAC5C;wBACA,MAAM,IAAI,CAAC;oBACb,OAAO,IAAI,WAAW,QAAQ,CAAC,YAAY;wBACzC,cAAc;4BACZ,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,QAAQ,QAAQ,WAAW,GAAG,YAAY;wBAC5C;wBACA,MAAM,IAAI,CAAC;oBACb,OAAO,IAAI,WAAW,QAAQ,CAAC,YAAY;wBACzC,cAAc;4BACZ,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,QAAQ,QAAQ,WAAW,GAAG,YAAY;wBAC5C;wBACA,MAAM,IAAI,CAAC;oBACb,OAAO,IAAI,aAAa;wBACtB,YAAY,OAAO,IAAI,OAAO;oBAChC;gBACF,OAAO,IAAI,KAAK,QAAQ,CAAC,SAAS;oBAChC,IAAI,aAAa;wBACf,YAAY,MAAM,GAAG;wBACrB,YAAY,OAAO,IAAI,OAAO,KAAK,OAAO,CAAC,QAAQ,IAAI,IAAI;oBAC7D;gBACF;YACF;YAEA,UAAU;YACV,MAAM,WAAW,QAAQ,KAAK,CAAC;YAC/B,MAAM,WAAW,UAAU,CAAC,EAAE,IAAI;YAElC,SAAS;YACT,MAAM,aAAa,QAAQ,KAAK,CAAC;YACjC,IAAI,YAA8B;YAElC,IAAI,cAAc,UAAU,CAAC,EAAE,EAAE;gBAC/B,IAAI;oBACF,YAAY,KAAK,KAAK,CAAC,UAAU,CAAC,EAAE;oBACpC,IAAI,aAAa,UAAU,IAAI,EAAE;wBAC/B,MAAM,IAAI,CAAC;4BACT,MAAM;4BACN,OAAO;4BACP,SAAS,CAAC,KAAK,EAAE,UAAU,KAAK,CAAC,IAAI,CAAC;4BACtC,QAAQ;4BACR,MAAM;wBACR;oBACF;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;YACF;YAEA,OAAO;gBAAE,eAAe;gBAAO;gBAAW;gBAAU;YAAS;QAC/D;mCAAG;QAAC,QAAQ,OAAO;QAAE,QAAQ,WAAW;KAAC;IAEzC,SAAS;IACT,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;gDAAE;YACzB,IAAI,UAAU;gBACZ,OAAO;oBACL,MAAM,2SAAA,CAAA,cAAW;oBACjB,OAAO;oBACP,SAAS;oBACT,aAAa;oBACb,QAAQ;gBACV;YACF;YAEA,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO;oBACL,MAAM,yRAAA,CAAA,OAAI;oBACV,OAAO;oBACP,SAAS;oBACT,aAAa;oBACb,QAAQ;gBACV;YACF;YAEA,OAAO;gBACL,MAAM,kTAAA,CAAA,cAAW;gBACjB,OAAO;gBACP,SAAS;gBACT,aAAa;gBACb,QAAQ;YACV;QACF;+CAAG;QAAC;QAAU,QAAQ,WAAW;KAAC;IAElC,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,QAAQ;YAC1B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,iBAAiB;IACnB;IAEA,MAAM,aAAa,WAAW,IAAI;IAElC,qBACE,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;kBAE7C,cAAA,sSAAC,mIAAA,CAAA,OAAI;YAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,+DACA,WAAW,WAAW,EACtB,WAAW,OAAO;;gBAGjB,QAAQ,WAAW,kBAClB,sSAAC,kJAAA,CAAA,aAAU;oBACT,MAAM;oBACN,UAAU;oBACV,WAAU;oBACV,SAAQ;;;;;;8BAIZ,sSAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,sSAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,iSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,QAAQ,WAAW,kBAClB,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;gDAAC;gDAC9B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;gDAAS;;;;;;;;;;;;kDAIlD,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,wJAAA,CAAA,cAAW;gDACV,WAAU;gDACV,UAAU,QAAQ,WAAW;0DAC9B;;;;;;0DAGD,sSAAC;gDAAI,WAAU;;kEACb,sSAAC,oIAAA,CAAA,QAAK;wDACJ,SAAS,WAAW,gBAAgB,QAAQ,WAAW,GAAG,YAAY;wDACtE,WAAU;;0EAEV,sSAAC;gEAAW,WAAU;;;;;;4DACrB,WAAW,MAAM;;;;;;;oDAEnB,cAAc,MAAM,GAAG,mBACtB,sSAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;;4DAChC,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;4DAAC;4DAAE,cAAc,MAAM;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7F,sSAAC;gCAAI,WAAU;;oCACZ,0BACC,sSAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC;wCAC7C,WAAU;kDAEV,cAAA,sSAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;oCAGnB,2BACC,sSAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;kDAEV,cAAA,sSAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO1B,sSAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;wBAEpB,cAAc,MAAM,GAAG,mBACtB,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,ySAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAInC,sSAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,sSAAC;4CAEC,MAAM;4CACN,OAAO;4CACP,YAAY,cAAc,GAAG,CAAC;4CAC9B,UAAU,IAAM,WAAW;4CAC3B,aAAa,QAAQ,WAAW,IAAI,UAAU,cAAc,MAAM,GAAG;2CALhE;;;;;;;;;;;;;;;;wBAad,0BACC,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;;8CAEV,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG9B,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAI,WAAU;sDACb,cAAA,sSAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;sDAElC,sSAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC;4CAC7C,WAAU;sDAEV,cAAA,sSAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wBAOvB,aAAa,UAAU,IAAI,IAAI,UAAU,IAAI,CAAC,MAAM,GAAG,mBACtD,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;;8CAEV,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAI,WAAU;;8DACb,sSAAC,2RAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAY;8DAE7B,sSAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;;wDAClC,UAAU,KAAK;wDAAC;;;;;;;;;;;;;sDAIrB,sSAAC;4CAAI,WAAU;sDACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAC;gDAC/B,WAAU;0DAET,cAAc,SAAS;;;;;;;;;;;;;;;;;8CAK9B,sSAAC,qSAAA,CAAA,kBAAe;oCAAC,MAAK;8CACnB,4BACC,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,MAAM;4CAAE,SAAS;wCAAE;wCACnB,WAAU;kDAEV,cAAA,sSAAC,6IAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,sSAAC;gDAAI,WAAU;0DACZ,KAAK,SAAS,CAAC,UAAU,IAAI,EAAE,MAAM;;;;;;;;;;;uCARtC;;;;6DAaN,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,MAAM;4CAAE,SAAS;wCAAE;kDAEnB,cAAA,sSAAC;4CAAU,MAAM,UAAU,IAAI;;;;;;uCAL3B;;;;;;;;;;;;;;;;wBAab,QAAQ,WAAW,kBAClB,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,WAAU;sCAEV,cAAA,sSAAC,6JAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1C;GA5VS;KAAA;AA8VT,WAAW;AACX,SAAS,iBAAiB,EACxB,IAAI,EACJ,KAAK,EACL,UAAU,EACV,QAAQ,EACR,WAAW,EAOZ;IACC,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAsB,OAAO,6RAAA,CAAA,SAAM;YACxC,KAAK;gBAAmB,OAAO,iSAAA,CAAA,WAAQ;YACvC,KAAK;gBAAkB,OAAO,yRAAA,CAAA,OAAI;YAClC,KAAK;gBAAa,OAAO,uRAAA,CAAA,MAAG;YAC5B,KAAK;gBAAU,OAAO,2RAAA,CAAA,QAAK;YAC3B;gBAAS,OAAO,2RAAA,CAAA,QAAK;QACvB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,WAAW,YAAY,KAAK,IAAI;IAEtC,qBACE,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,OAAO,QAAQ;QAAI;QACjC,WAAU;kBAEV,cAAA,sSAAC,0IAAA,CAAA,cAAW;YAAC,MAAM;YAAY,cAAc;;8BAC3C,sSAAC,0IAAA,CAAA,qBAAkB;oBAAC,OAAO;8BACzB,cAAA,sSAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;;0CAEV,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAS,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,eAAe,KAAK,MAAM;;;;;;4CAC5D,eAAe,KAAK,MAAM,KAAK,2BAC9B,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;gDAAC;gDAC9B,YAAY;oDAAE,UAAU;oDAAK,QAAQ;gDAAS;;;;;;;;;;;;kDAIpD,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAK,WAAU;0DAAuB,KAAK,KAAK;;;;;;0DACjD,sSAAC;gDAAK,WAAU;0DACb,KAAK,MAAM,KAAK,YAAY,WAC5B,KAAK,MAAM,KAAK,cAAc,QAC9B,KAAK,MAAM,KAAK,UAAU,SAAS;;;;;;;;;;;;;;;;;;0CAI1C,sSAAC;gCAAI,WAAU;;oCACZ,KAAK,MAAM,KAAK,6BACf,sSAAC,kTAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAExB,KAAK,MAAM,KAAK,yBACf,sSAAC,2SAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAExB,KAAK,MAAM,KAAK,aAAa,6BAC5B,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,QAAQ;wCAAI;wCACvB,YAAY;4CAAE,UAAU;4CAAG,QAAQ;4CAAU,MAAM;wCAAS;kDAE5D,cAAA,sSAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;oCAGnB,2BACC,sSAAC,uSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,sSAAC,2SAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAM/B,sSAAC,0IAAA,CAAA,qBAAkB;8BACjB,cAAA,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,wIAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,sSAAC;gCAAI,WAAU;0CACZ,KAAK,OAAO;;;;;;4BAEd,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,0BAC1B,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAGhE,sSAAC;wCAAI,WAAU;;4CACZ,KAAK,SAAS,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,MAAM;4CAClD,KAAK,IAAI,CAAC,IAAI,EAAE,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD;MAlHS;AAoHT,SAAS;AACT,SAAS,UAAU,EAAE,IAAI,EAAmC;IAC1D,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,sSAAC;YAAI,WAAU;sBAAyC;;;;;;IAI5D;IAEA,MAAM,UAAU,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;IACnC,MAAM,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,UAAU;IAEjD,qBACE,sSAAC;QAAI,WAAU;;0BACb,sSAAC,6IAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,sSAAC;;sCACC,sSAAC;sCACC,cAAA,sSAAC;0CACE,QAAQ,GAAG,CAAC,CAAC,uBACZ,sSAAC;wCAAuB,WAAU;kDAC/B;uCADa;;;;;;;;;;;;;;;sCAMtB,sSAAC;sCACE,YAAY,GAAG,CAAC,CAAC,KAAK,sBACrB,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;oCAER,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAK;oCAClC,WAAU;8CAET,QAAQ,GAAG,CAAC,CAAC,uBACZ,sSAAC;4CAAuB,WAAU;sDAC/B,OAAO,GAAG,CAAC,OAAO,KAAK,WACpB,KAAK,SAAS,CAAC,GAAG,CAAC,OAAO,IAC1B,OAAO,GAAG,CAAC,OAAO,IAAI;2CAHZ;;;;;mCAPb;;;;;;;;;;;;;;;;;;;;;YAoBd,KAAK,MAAM,GAAG,oBACb,sSAAC;gBAAI,WAAU;;oBAA4D;oBAC7D,KAAK,MAAM;oBAAC;;;;;;;;;;;;;AAKlC;MAvDS", "debugId": null}}, {"offset": {"line": 3535, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/app/chat/components/message-list-view.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\n// SPDX-License-Identifier: MIT\n\nimport { LoadingOutlined } from \"@ant-design/icons\";\nimport { motion } from \"framer-motion\";\nimport {\n  Download,\n  Headphones,\n  ChevronDown,\n  ChevronRight,\n  Lightbulb,\n} from \"lucide-react\";\nimport React, { useCallback, useMemo, useRef, useState } from \"react\";\n\nimport { LoadingAnimation } from \"~/components/deer-flow/loading-animation\";\nimport { Markdown } from \"~/components/deer-flow/markdown\";\nimport { RainbowText } from \"~/components/deer-flow/rainbow-text\";\nimport { RollingText } from \"~/components/deer-flow/rolling-text\";\nimport {\n  ScrollContainer,\n  type ScrollContainerRef,\n} from \"~/components/deer-flow/scroll-container\";\nimport { Tooltip } from \"~/components/deer-flow/tooltip\";\nimport { Button } from \"~/components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardFooter,\n  CardHeader,\n  CardTitle,\n} from \"~/components/ui/card\";\nimport {\n  Collapsible,\n  CollapsibleContent,\n  CollapsibleTrigger,\n} from \"~/components/ui/collapsible\";\n\nimport { DataAnalysisCard } from \"./data-analysis-card\";\nimport type { Message, Option } from \"~/core/messages\";\nimport {\n  closeResearch,\n  openResearch,\n  useLastFeedbackMessageId,\n  useLastInterruptMessage,\n  useMessage,\n  useMessageIds,\n  useResearchMessage,\n  useStore,\n} from \"~/core/store\";\nimport { parseJSON } from \"~/core/utils\";\nimport { cn } from \"~/lib/utils\";\n\nexport function MessageListView({\n  className,\n  onFeedback,\n  onSendMessage,\n}: {\n  className?: string;\n  onFeedback?: (feedback: { option: Option }) => void;\n  onSendMessage?: (\n    message: string,\n    options?: { interruptFeedback?: string },\n  ) => void;\n}) {\n  const scrollContainerRef = useRef<ScrollContainerRef>(null);\n  const messageIds = useMessageIds();\n  const interruptMessage = useLastInterruptMessage();\n  const waitingForFeedbackMessageId = useLastFeedbackMessageId();\n  const responding = useStore((state) => state.responding);\n  const noOngoingResearch = useStore(\n    (state) => state.ongoingResearchId === null,\n  );\n  const ongoingResearchIsOpen = useStore(\n    (state) => state.ongoingResearchId === state.openResearchId,\n  );\n\n  const handleToggleResearch = useCallback(() => {\n    // Fix the issue where auto-scrolling to the bottom\n    // occasionally fails when toggling research.\n    const timer = setTimeout(() => {\n      if (scrollContainerRef.current) {\n        scrollContainerRef.current.scrollToBottom();\n      }\n    }, 500);\n    return () => {\n      clearTimeout(timer);\n    };\n  }, []);\n\n  return (\n    <ScrollContainer\n      className={cn(\"flex h-full w-full flex-col overflow-hidden\", className)}\n      scrollShadowColor=\"var(--app-background)\"\n      autoScrollToBottom\n      ref={scrollContainerRef}\n    >\n      <ul className=\"flex flex-col\">\n        {messageIds.map((messageId) => (\n          <MessageListItem\n            key={messageId}\n            messageId={messageId}\n            waitForFeedback={waitingForFeedbackMessageId === messageId}\n            interruptMessage={interruptMessage}\n            onFeedback={onFeedback}\n            onSendMessage={onSendMessage}\n            onToggleResearch={handleToggleResearch}\n          />\n        ))}\n        <div className=\"flex h-8 w-full shrink-0\"></div>\n      </ul>\n      {responding && (noOngoingResearch || !ongoingResearchIsOpen) && (\n        <LoadingAnimation className=\"ml-4\" />\n      )}\n    </ScrollContainer>\n  );\n}\n\nfunction MessageListItem({\n  className,\n  messageId,\n  waitForFeedback,\n  interruptMessage,\n  onFeedback,\n  onSendMessage,\n  onToggleResearch,\n}: {\n  className?: string;\n  messageId: string;\n  waitForFeedback?: boolean;\n  onFeedback?: (feedback: { option: Option }) => void;\n  interruptMessage?: Message | null;\n  onSendMessage?: (\n    message: string,\n    options?: { interruptFeedback?: string },\n  ) => void;\n  onToggleResearch?: () => void;\n}) {\n  const message = useMessage(messageId);\n  const researchIds = useStore((state) => state.researchIds);\n  const startOfResearch = useMemo(() => {\n    return researchIds.includes(messageId);\n  }, [researchIds, messageId]);\n  if (message) {\n    if (\n      message.role === \"user\" ||\n      message.agent === \"coordinator\" ||\n      message.agent === \"planner\" ||\n      message.agent === \"podcast\" ||\n      message.agent === \"default_agent\" ||\n      message.agent === \"data_analyst\" ||\n      startOfResearch\n    ) {\n      let content: React.ReactNode;\n      if (message.agent === \"planner\") {\n        content = (\n          <div className=\"w-full px-4\">\n            <PlanCard\n              message={message}\n              waitForFeedback={waitForFeedback}\n              interruptMessage={interruptMessage}\n              onFeedback={onFeedback}\n              onSendMessage={onSendMessage}\n            />\n          </div>\n        );\n      } else if (message.agent === \"podcast\") {\n        content = (\n          <div className=\"w-full px-4\">\n            <PodcastCard message={message} />\n          </div>\n        );\n      } else if (message.agent === \"data_analyst\") {\n        content = (\n          <div className=\"w-full px-4\">\n            <DataAnalysisCard message={message} />\n          </div>\n        );\n      } else if (startOfResearch) {\n        content = (\n          <div className=\"w-full px-4\">\n            <ResearchCard\n              researchId={message.id}\n              onToggleResearch={onToggleResearch}\n            />\n          </div>\n        );\n      } else {\n        content = message.content ? (\n          <div\n            className={cn(\n              \"flex w-full px-4\",\n              message.role === \"user\" && \"justify-end\",\n              className,\n            )}\n          >\n            <MessageBubble message={message}>\n              <div className=\"flex w-full flex-col text-wrap break-words\">\n                <Markdown\n                  className={cn(\n                    message.role === \"user\" &&\n                      \"prose-invert not-dark:text-secondary dark:text-inherit\",\n                  )}\n                >\n                  {message?.content}\n                </Markdown>\n              </div>\n            </MessageBubble>\n          </div>\n        ) : null;\n      }\n      if (content) {\n        return (\n          <motion.li\n            className=\"mt-10\"\n            key={messageId}\n            initial={{ opacity: 0, y: 24 }}\n            animate={{ opacity: 1, y: 0 }}\n            style={{ transition: \"all 0.2s ease-out\" }}\n            transition={{\n              duration: 0.2,\n              ease: \"easeOut\",\n            }}\n          >\n            {content}\n          </motion.li>\n        );\n      }\n    }\n    return null;\n  }\n}\n\nfunction MessageBubble({\n  className,\n  message,\n  children,\n}: {\n  className?: string;\n  message: Message;\n  children: React.ReactNode;\n}) {\n  return (\n    <div\n      className={cn(\n        `group flex w-fit max-w-[85%] flex-col rounded-2xl px-4 py-3 text-nowrap shadow`,\n        message.role === \"user\" && \"bg-brand rounded-ee-none\",\n        message.role === \"assistant\" && \"bg-card rounded-es-none\",\n        className,\n      )}\n    >\n      {children}\n    </div>\n  );\n}\n\nfunction ResearchCard({\n  className,\n  researchId,\n  onToggleResearch,\n}: {\n  className?: string;\n  researchId: string;\n  onToggleResearch?: () => void;\n}) {\n  const reportId = useStore((state) => state.researchReportIds.get(researchId));\n  const hasReport = reportId !== undefined;\n  const reportGenerating = useStore(\n    (state) => hasReport && state.messages.get(reportId)!.isStreaming,\n  );\n  const openResearchId = useStore((state) => state.openResearchId);\n  const state = useMemo(() => {\n    if (hasReport) {\n      return reportGenerating ? \"Generating report...\" : \"Report generated\";\n    }\n    return \"Researching...\";\n  }, [hasReport, reportGenerating]);\n  const msg = useResearchMessage(researchId);\n  const title = useMemo(() => {\n    if (msg) {\n      return parseJSON(msg.content ?? \"\", { title: \"\" }).title;\n    }\n    return undefined;\n  }, [msg]);\n  const handleOpen = useCallback(() => {\n    if (openResearchId === researchId) {\n      closeResearch();\n    } else {\n      openResearch(researchId);\n    }\n    onToggleResearch?.();\n  }, [openResearchId, researchId, onToggleResearch]);\n  return (\n    <Card className={cn(\"w-full\", className)}>\n      <CardHeader>\n        <CardTitle>\n          <RainbowText animated={state !== \"Report generated\"}>\n            {title !== undefined && title !== \"\" ? title : \"Deep Research\"}\n          </RainbowText>\n        </CardTitle>\n      </CardHeader>\n      <CardFooter>\n        <div className=\"flex w-full\">\n          <RollingText className=\"text-muted-foreground flex-grow text-sm\">\n            {state}\n          </RollingText>\n          <Button\n            variant={!openResearchId ? \"default\" : \"outline\"}\n            onClick={handleOpen}\n          >\n            {researchId !== openResearchId ? \"Open\" : \"Close\"}\n          </Button>\n        </div>\n      </CardFooter>\n    </Card>\n  );\n}\n\nfunction ThoughtBlock({\n  className,\n  content,\n  isStreaming,\n  hasMainContent,\n}: {\n  className?: string;\n  content: string;\n  isStreaming?: boolean;\n  hasMainContent?: boolean;\n}) {\n  const [isOpen, setIsOpen] = useState(true);\n\n  const [hasAutoCollapsed, setHasAutoCollapsed] = useState(false);\n\n  React.useEffect(() => {\n    if (hasMainContent && !hasAutoCollapsed) {\n      setIsOpen(false);\n      setHasAutoCollapsed(true);\n    }\n  }, [hasMainContent, hasAutoCollapsed]);\n\n  if (!content || content.trim() === \"\") {\n    return null;\n  }\n\n  return (\n    <div className={cn(\"mb-6 w-full\", className)}>\n      <Collapsible open={isOpen} onOpenChange={setIsOpen}>\n        <CollapsibleTrigger asChild>\n          <Button\n            variant=\"ghost\"\n            className={cn(\n              \"h-auto w-full justify-start rounded-xl border px-6 py-4 text-left transition-all duration-200\",\n              \"hover:bg-accent hover:text-accent-foreground\",\n              isStreaming\n                ? \"border-primary/20 bg-primary/5 shadow-sm\"\n                : \"border-border bg-card\",\n            )}\n          >\n            <div className=\"flex w-full items-center gap-3\">\n              <Lightbulb\n                size={18}\n                className={cn(\n                  \"shrink-0 transition-colors duration-200\",\n                  isStreaming ? \"text-primary\" : \"text-muted-foreground\",\n                )}\n              />\n              <span\n                className={cn(\n                  \"leading-none font-semibold transition-colors duration-200\",\n                  isStreaming ? \"text-primary\" : \"text-foreground\",\n                )}\n              >\n                Deep Thinking\n              </span>\n              {isStreaming && <LoadingAnimation className=\"ml-2 scale-75\" />}\n              <div className=\"flex-grow\" />\n              {isOpen ? (\n                <ChevronDown\n                  size={16}\n                  className=\"text-muted-foreground transition-transform duration-200\"\n                />\n              ) : (\n                <ChevronRight\n                  size={16}\n                  className=\"text-muted-foreground transition-transform duration-200\"\n                />\n              )}\n            </div>\n          </Button>\n        </CollapsibleTrigger>\n        <CollapsibleContent className=\"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:slide-up-2 data-[state=open]:slide-down-2 mt-3\">\n          <Card\n            className={cn(\n              \"transition-all duration-200\",\n              isStreaming ? \"border-primary/20 bg-primary/5\" : \"border-border\",\n            )}\n          >\n            <CardContent>\n              <div className=\"flex h-40 w-full overflow-y-auto\">\n                <ScrollContainer\n                  className={cn(\n                    \"flex h-full w-full flex-col overflow-hidden\",\n                    className,\n                  )}\n                  scrollShadow={false}\n                  autoScrollToBottom\n                >\n                  <Markdown\n                    className={cn(\n                      \"prose dark:prose-invert max-w-none transition-colors duration-200\",\n                      isStreaming ? \"prose-primary\" : \"opacity-80\",\n                    )}\n                    animated={isStreaming}\n                  >\n                    {content}\n                  </Markdown>\n                </ScrollContainer>\n              </div>\n            </CardContent>\n          </Card>\n        </CollapsibleContent>\n      </Collapsible>\n    </div>\n  );\n}\n\nconst GREETINGS = [\"Cool\", \"Sounds great\", \"Looks good\", \"Great\", \"Awesome\"];\nfunction PlanCard({\n  className,\n  message,\n  interruptMessage,\n  onFeedback,\n  waitForFeedback,\n  onSendMessage,\n}: {\n  className?: string;\n  message: Message;\n  interruptMessage?: Message | null;\n  onFeedback?: (feedback: { option: Option }) => void;\n  onSendMessage?: (\n    message: string,\n    options?: { interruptFeedback?: string },\n  ) => void;\n  waitForFeedback?: boolean;\n}) {\n  const plan = useMemo<{\n    title?: string;\n    thought?: string;\n    steps?: { title?: string; description?: string }[];\n  }>(() => {\n    return parseJSON(message.content ?? \"\", {});\n  }, [message.content]);\n\n  const reasoningContent = message.reasoningContent;\n  const hasMainContent = Boolean(\n    message.content && message.content.trim() !== \"\",\n  );\n\n  // 判断是否正在思考：有推理内容但还没有主要内容\n  const isThinking = Boolean(reasoningContent && !hasMainContent);\n\n  // 判断是否应该显示计划：有主要内容就显示（无论是否还在流式传输）\n  const shouldShowPlan = hasMainContent;\n  const handleAccept = useCallback(async () => {\n    if (onSendMessage) {\n      onSendMessage(\n        `${GREETINGS[Math.floor(Math.random() * GREETINGS.length)]}! ${Math.random() > 0.5 ? \"Let's get started.\" : \"Let's start.\"}`,\n        {\n          interruptFeedback: \"accepted\",\n        },\n      );\n    }\n  }, [onSendMessage]);\n  return (\n    <div className={cn(\"w-full\", className)}>\n      {reasoningContent && (\n        <ThoughtBlock\n          content={reasoningContent}\n          isStreaming={isThinking}\n          hasMainContent={hasMainContent}\n        />\n      )}\n      {shouldShowPlan && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3, ease: \"easeOut\" }}\n        >\n          <Card className=\"w-full\">\n            <CardHeader>\n              <CardTitle>\n                <Markdown animated={message.isStreaming}>\n                  {`### ${\n                    plan.title !== undefined && plan.title !== \"\"\n                      ? plan.title\n                      : \"Deep Research\"\n                  }`}\n                </Markdown>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <Markdown className=\"opacity-80\" animated={message.isStreaming}>\n                {plan.thought}\n              </Markdown>\n              {plan.steps && (\n                <ul className=\"my-2 flex list-decimal flex-col gap-4 border-l-[2px] pl-8\">\n                  {plan.steps.map((step, i) => (\n                    <li key={`step-${i}`}>\n                      <h3 className=\"mb text-lg font-medium\">\n                        <Markdown animated={message.isStreaming}>\n                          {step.title}\n                        </Markdown>\n                      </h3>\n                      <div className=\"text-muted-foreground text-sm\">\n                        <Markdown animated={message.isStreaming}>\n                          {step.description}\n                        </Markdown>\n                      </div>\n                    </li>\n                  ))}\n                </ul>\n              )}\n            </CardContent>\n            <CardFooter className=\"flex justify-end\">\n              {!message.isStreaming && interruptMessage?.options?.length && (\n                <motion.div\n                  className=\"flex gap-2\"\n                  initial={{ opacity: 0, y: 12 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3, delay: 0.3 }}\n                >\n                  {interruptMessage?.options.map((option) => (\n                    <Button\n                      key={option.value}\n                      variant={\n                        option.value === \"accepted\" ? \"default\" : \"outline\"\n                      }\n                      disabled={!waitForFeedback}\n                      onClick={() => {\n                        if (option.value === \"accepted\") {\n                          void handleAccept();\n                        } else {\n                          onFeedback?.({\n                            option,\n                          });\n                        }\n                      }}\n                    >\n                      {option.text}\n                    </Button>\n                  ))}\n                </motion.div>\n              )}\n            </CardFooter>\n          </Card>\n        </motion.div>\n      )}\n    </div>\n  );\n}\n\nfunction PodcastCard({\n  className,\n  message,\n}: {\n  className?: string;\n  message: Message;\n}) {\n  const data = useMemo(() => {\n    return JSON.parse(message.content ?? \"\");\n  }, [message.content]);\n  const title = useMemo<string | undefined>(() => data?.title, [data]);\n  const audioUrl = useMemo<string | undefined>(() => data?.audioUrl, [data]);\n  const isGenerating = useMemo(() => {\n    return message.isStreaming;\n  }, [message.isStreaming]);\n  const hasError = useMemo(() => {\n    return data?.error !== undefined;\n  }, [data]);\n  const [isPlaying, setIsPlaying] = useState(false);\n  return (\n    <Card className={cn(\"w-[508px]\", className)}>\n      <CardHeader>\n        <div className=\"text-muted-foreground flex items-center justify-between text-sm\">\n          <div className=\"flex items-center gap-2\">\n            {isGenerating ? <LoadingOutlined /> : <Headphones size={16} />}\n            {!hasError ? (\n              <RainbowText animated={isGenerating}>\n                {isGenerating\n                  ? \"Generating podcast...\"\n                  : isPlaying\n                    ? \"Now playing podcast...\"\n                    : \"Podcast\"}\n              </RainbowText>\n            ) : (\n              <div className=\"text-red-500\">\n                Error when generating podcast. Please try again.\n              </div>\n            )}\n          </div>\n          {!hasError && !isGenerating && (\n            <div className=\"flex\">\n              <Tooltip title=\"Download podcast\">\n                <Button variant=\"ghost\" size=\"icon\" asChild>\n                  <a\n                    href={audioUrl}\n                    download={`${(title ?? \"podcast\").replaceAll(\" \", \"-\")}.mp3`}\n                  >\n                    <Download size={16} />\n                  </a>\n                </Button>\n              </Tooltip>\n            </div>\n          )}\n        </div>\n        <CardTitle>\n          <div className=\"text-lg font-medium\">\n            <RainbowText animated={isGenerating}>{title}</RainbowText>\n          </div>\n        </CardTitle>\n      </CardHeader>\n      <CardContent>\n        {audioUrl ? (\n          <audio\n            className=\"w-full\"\n            src={audioUrl}\n            controls\n            onPlay={() => setIsPlaying(true)}\n            onPause={() => setIsPlaying(false)}\n          />\n        ) : (\n          <div className=\"w-full\"></div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAEA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AAOA;AAMA;AAEA;AAAA;AAUA;AAAA;AACA;;;;;;;;;;;;;;;;;;;;AAEO,SAAS,gBAAgB,EAC9B,SAAS,EACT,UAAU,EACV,aAAa,EAQd;;IACC,MAAM,qBAAqB,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAsB;IACtD,MAAM,aAAa,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,mBAAmB,CAAA,GAAA,gIAAA,CAAA,0BAAuB,AAAD;IAC/C,MAAM,8BAA8B,CAAA,GAAA,gIAAA,CAAA,2BAAwB,AAAD;IAC3D,MAAM,aAAa,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;gDAAE,CAAC,QAAU,MAAM,UAAU;;IACvD,MAAM,oBAAoB,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;uDAC/B,CAAC,QAAU,MAAM,iBAAiB,KAAK;;IAEzC,MAAM,wBAAwB,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;2DACnC,CAAC,QAAU,MAAM,iBAAiB,KAAK,MAAM,cAAc;;IAG7D,MAAM,uBAAuB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;6DAAE;YACvC,mDAAmD;YACnD,6CAA6C;YAC7C,MAAM,QAAQ;2EAAW;oBACvB,IAAI,mBAAmB,OAAO,EAAE;wBAC9B,mBAAmB,OAAO,CAAC,cAAc;oBAC3C;gBACF;0EAAG;YACH;qEAAO;oBACL,aAAa;gBACf;;QACF;4DAAG,EAAE;IAEL,qBACE,sSAAC,4JAAA,CAAA,kBAAe;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC7D,mBAAkB;QAClB,kBAAkB;QAClB,KAAK;;0BAEL,sSAAC;gBAAG,WAAU;;oBACX,WAAW,GAAG,CAAC,CAAC,0BACf,sSAAC;4BAEC,WAAW;4BACX,iBAAiB,gCAAgC;4BACjD,kBAAkB;4BAClB,YAAY;4BACZ,eAAe;4BACf,kBAAkB;2BANb;;;;;kCAST,sSAAC;wBAAI,WAAU;;;;;;;;;;;;YAEhB,cAAc,CAAC,qBAAqB,CAAC,qBAAqB,mBACzD,sSAAC,6JAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAIpC;GA/DgB;;QAaK,gIAAA,CAAA,gBAAa;QACP,gIAAA,CAAA,0BAAuB;QACZ,gIAAA,CAAA,2BAAwB;QACzC,gIAAA,CAAA,WAAQ;QACD,gIAAA,CAAA,WAAQ;QAGJ,gIAAA,CAAA,WAAQ;;;KApBxB;AAiEhB,SAAS,gBAAgB,EACvB,SAAS,EACT,SAAS,EACT,eAAe,EACf,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,gBAAgB,EAYjB;;IACC,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;iDAAE,CAAC,QAAU,MAAM,WAAW;;IACzD,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;oDAAE;YAC9B,OAAO,YAAY,QAAQ,CAAC;QAC9B;mDAAG;QAAC;QAAa;KAAU;IAC3B,IAAI,SAAS;QACX,IACE,QAAQ,IAAI,KAAK,UACjB,QAAQ,KAAK,KAAK,iBAClB,QAAQ,KAAK,KAAK,aAClB,QAAQ,KAAK,KAAK,aAClB,QAAQ,KAAK,KAAK,mBAClB,QAAQ,KAAK,KAAK,kBAClB,iBACA;YACA,IAAI;YACJ,IAAI,QAAQ,KAAK,KAAK,WAAW;gBAC/B,wBACE,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;wBACC,SAAS;wBACT,iBAAiB;wBACjB,kBAAkB;wBAClB,YAAY;wBACZ,eAAe;;;;;;;;;;;YAIvB,OAAO,IAAI,QAAQ,KAAK,KAAK,WAAW;gBACtC,wBACE,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;wBAAY,SAAS;;;;;;;;;;;YAG5B,OAAO,IAAI,QAAQ,KAAK,KAAK,gBAAgB;gBAC3C,wBACE,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC,gKAAA,CAAA,mBAAgB;wBAAC,SAAS;;;;;;;;;;;YAGjC,OAAO,IAAI,iBAAiB;gBAC1B,wBACE,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;wBACC,YAAY,QAAQ,EAAE;wBACtB,kBAAkB;;;;;;;;;;;YAI1B,OAAO;gBACL,UAAU,QAAQ,OAAO,iBACvB,sSAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oBACA,QAAQ,IAAI,KAAK,UAAU,eAC3B;8BAGF,cAAA,sSAAC;wBAAc,SAAS;kCACtB,cAAA,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,iJAAA,CAAA,WAAQ;gCACP,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QAAQ,IAAI,KAAK,UACf;0CAGH,SAAS;;;;;;;;;;;;;;;;;;;;2BAKhB;YACN;YACA,IAAI,SAAS;gBACX,qBACE,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;oBACR,WAAU;oBAEV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,OAAO;wBAAE,YAAY;oBAAoB;oBACzC,YAAY;wBACV,UAAU;wBACV,MAAM;oBACR;8BAEC;mBATI;;;;;YAYX;QACF;QACA,OAAO;IACT;AACF;IAjHS;;QAoBS,gIAAA,CAAA,aAAU;QACN,gIAAA,CAAA,WAAQ;;;MArBrB;AAmHT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,EACP,QAAQ,EAKT;IACC,qBACE,sSAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAC,8EAA8E,CAAC,EAChF,QAAQ,IAAI,KAAK,UAAU,4BAC3B,QAAQ,IAAI,KAAK,eAAe,2BAChC;kBAGD;;;;;;AAGP;MArBS;AAuBT,SAAS,aAAa,EACpB,SAAS,EACT,UAAU,EACV,gBAAgB,EAKjB;;IACC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;2CAAE,CAAC,QAAU,MAAM,iBAAiB,CAAC,GAAG,CAAC;;IACjE,MAAM,YAAY,aAAa;IAC/B,MAAM,mBAAmB,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;mDAC9B,CAAC,QAAU,aAAa,MAAM,QAAQ,CAAC,GAAG,CAAC,UAAW,WAAW;;IAEnE,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;iDAAE,CAAC,QAAU,MAAM,cAAc;;IAC/D,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;uCAAE;YACpB,IAAI,WAAW;gBACb,OAAO,mBAAmB,yBAAyB;YACrD;YACA,OAAO;QACT;sCAAG;QAAC;QAAW;KAAiB;IAChC,MAAM,MAAM,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/B,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;uCAAE;YACpB,IAAI,KAAK;gBACP,OAAO,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD,EAAE,IAAI,OAAO,IAAI,IAAI;oBAAE,OAAO;gBAAG,GAAG,KAAK;YAC1D;YACA,OAAO;QACT;sCAAG;QAAC;KAAI;IACR,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;gDAAE;YAC7B,IAAI,mBAAmB,YAAY;gBACjC,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;YACd,OAAO;gBACL,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;YACf;YACA;QACF;+CAAG;QAAC;QAAgB;QAAY;KAAiB;IACjD,qBACE,sSAAC,mIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;0BAC5B,sSAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,sSAAC,mIAAA,CAAA,YAAS;8BACR,cAAA,sSAAC,wJAAA,CAAA,cAAW;wBAAC,UAAU,UAAU;kCAC9B,UAAU,aAAa,UAAU,KAAK,QAAQ;;;;;;;;;;;;;;;;0BAIrD,sSAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,sSAAC;oBAAI,WAAU;;sCACb,sSAAC,wJAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB;;;;;;sCAEH,sSAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,CAAC,iBAAiB,YAAY;4BACvC,SAAS;sCAER,eAAe,iBAAiB,SAAS;;;;;;;;;;;;;;;;;;;;;;;AAMtD;IA5DS;;QASU,gIAAA,CAAA,WAAQ;QAEA,gIAAA,CAAA,WAAQ;QAGV,gIAAA,CAAA,WAAQ;QAOnB,gIAAA,CAAA,qBAAkB;;;MArBvB;AA8DT,SAAS,aAAa,EACpB,SAAS,EACT,OAAO,EACP,WAAW,EACX,cAAc,EAMf;;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,sQAAA,CAAA,UAAK,CAAC,SAAS;kCAAC;YACd,IAAI,kBAAkB,CAAC,kBAAkB;gBACvC,UAAU;gBACV,oBAAoB;YACtB;QACF;iCAAG;QAAC;QAAgB;KAAiB;IAErC,IAAI,CAAC,WAAW,QAAQ,IAAI,OAAO,IAAI;QACrC,OAAO;IACT;IAEA,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;kBAChC,cAAA,sSAAC,0IAAA,CAAA,cAAW;YAAC,MAAM;YAAQ,cAAc;;8BACvC,sSAAC,0IAAA,CAAA,qBAAkB;oBAAC,OAAO;8BACzB,cAAA,sSAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iGACA,gDACA,cACI,6CACA;kCAGN,cAAA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,mSAAA,CAAA,YAAS;oCACR,MAAM;oCACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2CACA,cAAc,iBAAiB;;;;;;8CAGnC,sSAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6DACA,cAAc,iBAAiB;8CAElC;;;;;;gCAGA,6BAAe,sSAAC,6JAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;8CAC5C,sSAAC;oCAAI,WAAU;;;;;;gCACd,uBACC,sSAAC,2SAAA,CAAA,cAAW;oCACV,MAAM;oCACN,WAAU;;;;;yDAGZ,sSAAC,6SAAA,CAAA,eAAY;oCACX,MAAM;oCACN,WAAU;;;;;;;;;;;;;;;;;;;;;;8BAMpB,sSAAC,0IAAA,CAAA,qBAAkB;oBAAC,WAAU;8BAC5B,cAAA,sSAAC,mIAAA,CAAA,OAAI;wBACH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+BACA,cAAc,mCAAmC;kCAGnD,cAAA,sSAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC,4JAAA,CAAA,kBAAe;oCACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+CACA;oCAEF,cAAc;oCACd,kBAAkB;8CAElB,cAAA,sSAAC,iJAAA,CAAA,WAAQ;wCACP,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qEACA,cAAc,kBAAkB;wCAElC,UAAU;kDAET;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrB;IA1GS;MAAA;AA4GT,MAAM,YAAY;IAAC;IAAQ;IAAgB;IAAc;IAAS;CAAU;AAC5E,SAAS,SAAS,EAChB,SAAS,EACT,OAAO,EACP,gBAAgB,EAChB,UAAU,EACV,eAAe,EACf,aAAa,EAWd;;IACC,MAAM,OAAO,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;kCAIhB;YACD,OAAO,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,OAAO,IAAI,IAAI,CAAC;QAC3C;iCAAG;QAAC,QAAQ,OAAO;KAAC;IAEpB,MAAM,mBAAmB,QAAQ,gBAAgB;IACjD,MAAM,iBAAiB,QACrB,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,IAAI,OAAO;IAGhD,yBAAyB;IACzB,MAAM,aAAa,QAAQ,oBAAoB,CAAC;IAEhD,kCAAkC;IAClC,MAAM,iBAAiB;IACvB,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;8CAAE;YAC/B,IAAI,eAAe;gBACjB,cACE,GAAG,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE,CAAC,EAAE,EAAE,KAAK,MAAM,KAAK,MAAM,uBAAuB,gBAAgB,EAC5H;oBACE,mBAAmB;gBACrB;YAEJ;QACF;6CAAG;QAAC;KAAc;IAClB,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,kCACC,sSAAC;gBACC,SAAS;gBACT,aAAa;gBACb,gBAAgB;;;;;;YAGnB,gCACC,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAU;0BAE7C,cAAA,sSAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,sSAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,sSAAC,mIAAA,CAAA,YAAS;0CACR,cAAA,sSAAC,iJAAA,CAAA,WAAQ;oCAAC,UAAU,QAAQ,WAAW;8CACpC,CAAC,IAAI,EACJ,KAAK,KAAK,KAAK,aAAa,KAAK,KAAK,KAAK,KACvC,KAAK,KAAK,GACV,iBACJ;;;;;;;;;;;;;;;;sCAIR,sSAAC,mIAAA,CAAA,cAAW;;8CACV,sSAAC,iJAAA,CAAA,WAAQ;oCAAC,WAAU;oCAAa,UAAU,QAAQ,WAAW;8CAC3D,KAAK,OAAO;;;;;;gCAEd,KAAK,KAAK,kBACT,sSAAC;oCAAG,WAAU;8CACX,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,kBACrB,sSAAC;;8DACC,sSAAC;oDAAG,WAAU;8DACZ,cAAA,sSAAC,iJAAA,CAAA,WAAQ;wDAAC,UAAU,QAAQ,WAAW;kEACpC,KAAK,KAAK;;;;;;;;;;;8DAGf,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC,iJAAA,CAAA,WAAQ;wDAAC,UAAU,QAAQ,WAAW;kEACpC,KAAK,WAAW;;;;;;;;;;;;2CARd,CAAC,KAAK,EAAE,GAAG;;;;;;;;;;;;;;;;sCAgB5B,sSAAC,mIAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,CAAC,QAAQ,WAAW,IAAI,kBAAkB,SAAS,wBAClD,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CAEvC,kBAAkB,QAAQ,IAAI,CAAC,uBAC9B,sSAAC,qIAAA,CAAA,SAAM;wCAEL,SACE,OAAO,KAAK,KAAK,aAAa,YAAY;wCAE5C,UAAU,CAAC;wCACX,SAAS;4CACP,IAAI,OAAO,KAAK,KAAK,YAAY;gDAC/B,KAAK;4CACP,OAAO;gDACL,aAAa;oDACX;gDACF;4CACF;wCACF;kDAEC,OAAO,IAAI;uCAfP,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BvC;IApIS;MAAA;AAsIT,SAAS,YAAY,EACnB,SAAS,EACT,OAAO,EAIR;;IACC,MAAM,OAAO,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;qCAAE;YACnB,OAAO,KAAK,KAAK,CAAC,QAAQ,OAAO,IAAI;QACvC;oCAAG;QAAC,QAAQ,OAAO;KAAC;IACpB,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;sCAAsB,IAAM,MAAM;qCAAO;QAAC;KAAK;IACnE,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;yCAAsB,IAAM,MAAM;wCAAU;QAAC;KAAK;IACzE,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;6CAAE;YAC3B,OAAO,QAAQ,WAAW;QAC5B;4CAAG;QAAC,QAAQ,WAAW;KAAC;IACxB,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;yCAAE;YACvB,OAAO,MAAM,UAAU;QACzB;wCAAG;QAAC;KAAK;IACT,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,qBACE,sSAAC,mIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC/B,sSAAC,mIAAA,CAAA,aAAU;;kCACT,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;;oCACZ,6BAAe,sSAAC,qUAAA,CAAA,kBAAe;;;;6DAAM,sSAAC,qSAAA,CAAA,aAAU;wCAAC,MAAM;;;;;;oCACvD,CAAC,yBACA,sSAAC,wJAAA,CAAA,cAAW;wCAAC,UAAU;kDACpB,eACG,0BACA,YACE,2BACA;;;;;6DAGR,sSAAC;wCAAI,WAAU;kDAAe;;;;;;;;;;;;4BAKjC,CAAC,YAAY,CAAC,8BACb,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC,gJAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAO,OAAO;kDACzC,cAAA,sSAAC;4CACC,MAAM;4CACN,UAAU,GAAG,CAAC,SAAS,SAAS,EAAE,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC;sDAE5D,cAAA,sSAAC,iSAAA,CAAA,WAAQ;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5B,sSAAC,mIAAA,CAAA,YAAS;kCACR,cAAA,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,wJAAA,CAAA,cAAW;gCAAC,UAAU;0CAAe;;;;;;;;;;;;;;;;;;;;;;0BAI5C,sSAAC,mIAAA,CAAA,cAAW;0BACT,yBACC,sSAAC;oBACC,WAAU;oBACV,KAAK;oBACL,QAAQ;oBACR,QAAQ,IAAM,aAAa;oBAC3B,SAAS,IAAM,aAAa;;;;;yCAG9B,sSAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;IA3ES;MAAA", "debugId": null}}, {"offset": {"line": 4512, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/app/chat/components/messages-block.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { FastForward, Play } from \"lucide-react\";\r\nimport { useCallback, useRef, useState } from \"react\";\r\n\r\nimport { RainbowText } from \"~/components/deer-flow/rainbow-text\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport {\r\n  Card,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"~/components/ui/card\";\r\nimport { fastForwardReplay } from \"~/core/api\";\r\nimport { useReplayMetadata } from \"~/core/api/hooks\";\r\nimport type { Option, Resource } from \"~/core/messages\";\r\nimport { useReplay } from \"~/core/replay\";\r\nimport { sendMessage, useMessageIds, useStore } from \"~/core/store\";\r\nimport { env } from \"~/env\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport { ConversationStarter } from \"./conversation-starter\";\r\nimport { InputBox } from \"./input-box\";\r\nimport { MessageListView } from \"./message-list-view\";\r\nimport { Welcome } from \"./welcome\";\r\n\r\nexport function MessagesBlock({ className }: { className?: string }) {\r\n  const messageIds = useMessageIds();\r\n  const messageCount = messageIds.length;\r\n  const responding = useStore((state) => state.responding);\r\n  const { isReplay } = useReplay();\r\n  const { title: replayTitle, hasError: replayHasError } = useReplayMetadata();\r\n  const [replayStarted, setReplayStarted] = useState(false);\r\n  const abortControllerRef = useRef<AbortController | null>(null);\r\n  const [feedback, setFeedback] = useState<{ option: Option } | null>(null);\r\n  const handleSend = useCallback(\r\n    async (\r\n      message: string,\r\n      options?: {\r\n        interruptFeedback?: string;\r\n        resources?: Array<Resource>;\r\n      },\r\n    ) => {\r\n      const abortController = new AbortController();\r\n      abortControllerRef.current = abortController;\r\n      try {\r\n        await sendMessage(\r\n          message,\r\n          {\r\n            interruptFeedback:\r\n              options?.interruptFeedback ?? feedback?.option.value,\r\n            resources: options?.resources,\r\n          },\r\n          {\r\n            abortSignal: abortController.signal,\r\n          },\r\n        );\r\n      } catch {}\r\n    },\r\n    [feedback],\r\n  );\r\n  const handleCancel = useCallback(() => {\r\n    abortControllerRef.current?.abort();\r\n    abortControllerRef.current = null;\r\n  }, []);\r\n  const handleFeedback = useCallback(\r\n    (feedback: { option: Option }) => {\r\n      setFeedback(feedback);\r\n    },\r\n    [setFeedback],\r\n  );\r\n  const handleRemoveFeedback = useCallback(() => {\r\n    setFeedback(null);\r\n  }, [setFeedback]);\r\n  const handleStartReplay = useCallback(() => {\r\n    setReplayStarted(true);\r\n    void sendMessage();\r\n  }, [setReplayStarted]);\r\n  const [fastForwarding, setFastForwarding] = useState(false);\r\n  const handleFastForwardReplay = useCallback(() => {\r\n    setFastForwarding(!fastForwarding);\r\n    fastForwardReplay(!fastForwarding);\r\n  }, [fastForwarding]);\r\n  return (\r\n    <div className={cn(\"flex h-full flex-col\", className)}>\r\n      <MessageListView\r\n        className=\"flex flex-grow\"\r\n        onFeedback={handleFeedback}\r\n        onSendMessage={handleSend}\r\n      />\r\n      {!isReplay ? (\r\n        <div className=\"relative flex h-42 shrink-0 pb-4\">\r\n          {!responding && messageCount === 0 && (\r\n            <ConversationStarter\r\n              className=\"absolute top-[-218px] left-0\"\r\n              onSend={handleSend}\r\n            />\r\n          )}\r\n          <InputBox\r\n            className=\"h-full w-full\"\r\n            responding={responding}\r\n            feedback={feedback}\r\n            onSend={handleSend}\r\n            onCancel={handleCancel}\r\n            onRemoveFeedback={handleRemoveFeedback}\r\n          />\r\n        </div>\r\n      ) : (\r\n        <>\r\n          <div\r\n            className={cn(\r\n              \"fixed bottom-[calc(50vh+80px)] left-0 transition-all duration-500 ease-out\",\r\n              replayStarted && \"pointer-events-none scale-150 opacity-0\",\r\n            )}\r\n          >\r\n            <Welcome />\r\n          </div>\r\n          <motion.div\r\n            className=\"mb-4 h-fit w-full items-center justify-center\"\r\n            initial={{ opacity: 0, y: \"20vh\" }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5 }}\r\n          >\r\n            <Card\r\n              className={cn(\r\n                \"w-full transition-all duration-300\",\r\n                !replayStarted && \"translate-y-[-40vh]\",\r\n              )}\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex flex-grow items-center\">\r\n                  {responding && (\r\n                    <motion.div\r\n                      className=\"ml-3\"\r\n                      initial={{ opacity: 0, scale: 0.8 }}\r\n                      animate={{ opacity: 1, scale: 1 }}\r\n                      exit={{ opacity: 0, scale: 0.8 }}\r\n                      transition={{ duration: 0.3 }}\r\n                    >\r\n                      <video\r\n                        // Walking deer animation, designed by @liangzhaojun. Thank you for creating it!\r\n                        src=\"/images/walking_deer.webm\"\r\n                        autoPlay\r\n                        loop\r\n                        muted\r\n                        className=\"h-[42px] w-[42px] object-contain\"\r\n                      />\r\n                    </motion.div>\r\n                  )}\r\n                  <CardHeader className={cn(\"flex-grow\", responding && \"pl-3\")}>\r\n                    <CardTitle>\r\n                      <RainbowText animated={responding}>\r\n                        {responding ? \"Replaying\" : `${replayTitle}`}\r\n                      </RainbowText>\r\n                    </CardTitle>\r\n                    <CardDescription>\r\n                      <RainbowText animated={responding}>\r\n                        {responding\r\n                          ? \"DeerFlow is now replaying the conversation...\"\r\n                          : replayStarted\r\n                            ? \"The replay has been stopped.\"\r\n                            : `You're now in DeerFlow's replay mode. Click the \"Play\" button on the right to start.`}\r\n                      </RainbowText>\r\n                    </CardDescription>\r\n                  </CardHeader>\r\n                </div>\r\n                {!replayHasError && (\r\n                  <div className=\"pr-4\">\r\n                    {responding && (\r\n                      <Button\r\n                        className={cn(fastForwarding && \"animate-pulse\")}\r\n                        variant={fastForwarding ? \"default\" : \"outline\"}\r\n                        onClick={handleFastForwardReplay}\r\n                      >\r\n                        <FastForward size={16} />\r\n                        Fast Forward\r\n                      </Button>\r\n                    )}\r\n                    {!replayStarted && (\r\n                      <Button className=\"w-24\" onClick={handleStartReplay}>\r\n                        <Play size={16} />\r\n                        Play\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </Card>\r\n            {!replayStarted && env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY && (\r\n              <div className=\"text-muted-foreground w-full text-center text-xs\">\r\n                * This site is for demo purposes only. If you want to try your\r\n                own question, please{\" \"}\r\n                <a\r\n                  className=\"underline\"\r\n                  href=\"https://github.com/bytedance/deer-flow\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                >\r\n                  click here\r\n                </a>{\" \"}\r\n                to clone it locally and run it.\r\n              </div>\r\n            )}\r\n          </motion.div>\r\n        </>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AAAA;AACA;AAEA;AACA;AACA;AAMA;AAAA;AACA;AAEA;AAAA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAEO,SAAS,cAAc,EAAE,SAAS,EAA0B;;IACjE,MAAM,aAAa,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,eAAe,WAAW,MAAM;IACtC,MAAM,aAAa,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;8CAAE,CAAC,QAAU,MAAM,UAAU;;IACvD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,EAAE,OAAO,WAAW,EAAE,UAAU,cAAc,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;IACzE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,qBAAqB,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAA0B;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA6B;IACpE,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;iDAC3B,OACE,SACA;YAKA,MAAM,kBAAkB,IAAI;YAC5B,mBAAmB,OAAO,GAAG;YAC7B,IAAI;gBACF,MAAM,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD,EACd,SACA;oBACE,mBACE,SAAS,qBAAqB,UAAU,OAAO;oBACjD,WAAW,SAAS;gBACtB,GACA;oBACE,aAAa,gBAAgB,MAAM;gBACrC;YAEJ,EAAE,OAAM,CAAC;QACX;gDACA;QAAC;KAAS;IAEZ,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAAE;YAC/B,mBAAmB,OAAO,EAAE;YAC5B,mBAAmB,OAAO,GAAG;QAC/B;kDAAG,EAAE;IACL,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;qDAC/B,CAAC;YACC,YAAY;QACd;oDACA;QAAC;KAAY;IAEf,MAAM,uBAAuB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;2DAAE;YACvC,YAAY;QACd;0DAAG;QAAC;KAAY;IAChB,MAAM,oBAAoB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;wDAAE;YACpC,iBAAiB;YACjB,KAAK,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;QACjB;uDAAG;QAAC;KAAiB;IACrB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,0BAA0B,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;8DAAE;YAC1C,kBAAkB,CAAC;YACnB,CAAA,GAAA,6HAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC;QACrB;6DAAG;QAAC;KAAe;IACnB,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;;0BACzC,sSAAC,+JAAA,CAAA,kBAAe;gBACd,WAAU;gBACV,YAAY;gBACZ,eAAe;;;;;;YAEhB,CAAC,yBACA,sSAAC;gBAAI,WAAU;;oBACZ,CAAC,cAAc,iBAAiB,mBAC/B,sSAAC,+JAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,QAAQ;;;;;;kCAGZ,sSAAC,oJAAA,CAAA,WAAQ;wBACP,WAAU;wBACV,YAAY;wBACZ,UAAU;wBACV,QAAQ;wBACR,UAAU;wBACV,kBAAkB;;;;;;;;;;;qCAItB;;kCACE,sSAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8EACA,iBAAiB;kCAGnB,cAAA,sSAAC,+IAAA,CAAA,UAAO;;;;;;;;;;kCAEV,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAO;wBACjC,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,sSAAC,mIAAA,CAAA,OAAI;gCACH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sCACA,CAAC,iBAAiB;0CAGpB,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAI,WAAU;;gDACZ,4BACC,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAClC,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,MAAM;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAC/B,YAAY;wDAAE,UAAU;oDAAI;8DAE5B,cAAA,sSAAC;wDACC,gFAAgF;wDAChF,KAAI;wDACJ,QAAQ;wDACR,IAAI;wDACJ,KAAK;wDACL,WAAU;;;;;;;;;;;8DAIhB,sSAAC,mIAAA,CAAA,aAAU;oDAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,cAAc;;sEACnD,sSAAC,mIAAA,CAAA,YAAS;sEACR,cAAA,sSAAC,wJAAA,CAAA,cAAW;gEAAC,UAAU;0EACpB,aAAa,cAAc,GAAG,aAAa;;;;;;;;;;;sEAGhD,sSAAC,mIAAA,CAAA,kBAAe;sEACd,cAAA,sSAAC,wJAAA,CAAA,cAAW;gEAAC,UAAU;0EACpB,aACG,kDACA,gBACE,iCACA,CAAC,oFAAoF,CAAC;;;;;;;;;;;;;;;;;;;;;;;wCAKnG,CAAC,gCACA,sSAAC;4CAAI,WAAU;;gDACZ,4BACC,sSAAC,qIAAA,CAAA,SAAM;oDACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;oDAChC,SAAS,iBAAiB,YAAY;oDACtC,SAAS;;sEAET,sSAAC,2SAAA,CAAA,cAAW;4DAAC,MAAM;;;;;;wDAAM;;;;;;;gDAI5B,CAAC,+BACA,sSAAC,qIAAA,CAAA,SAAM;oDAAC,WAAU;oDAAO,SAAS;;sEAChC,sSAAC,yRAAA,CAAA,OAAI;4DAAC,MAAM;;;;;;wDAAM;;;;;;;;;;;;;;;;;;;;;;;;4BAQ7B,CAAC,iBAAiB,6GAAA,CAAA,MAAG,CAAC,+BAA+B,kBACpD,sSAAC;gCAAI,WAAU;;oCAAmD;oCAE3C;kDACrB,sSAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,KAAI;kDACL;;;;;;oCAEI;oCAAI;;;;;;;;;;;;;;;;;;;;;AASzB;GAtLgB;;QACK,gIAAA,CAAA,gBAAa;QAEb,gIAAA,CAAA,WAAQ;QACN,iIAAA,CAAA,YAAS;QAC2B,8HAAA,CAAA,oBAAiB;;;KAL5D", "debugId": null}}, {"offset": {"line": 4890, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/deer-flow/fav-icon.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport function FavIcon({\r\n  className,\r\n  url,\r\n  title,\r\n}: {\r\n  className?: string;\r\n  url: string;\r\n  title?: string;\r\n}) {\r\n  return (\r\n    <img\r\n      className={cn(\"bg-accent h-4 w-4 rounded-full shadow-sm\", className)}\r\n      width={16}\r\n      height={16}\r\n      src={new URL(url).origin + \"/favicon.ico\"}\r\n      alt={title}\r\n      onError={(e) => {\r\n        e.currentTarget.src =\r\n          \"https://perishablepress.com/wp/wp-content/images/2021/favicon-standard.png\";\r\n      }}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;;;AAEO,SAAS,QAAQ,EACtB,SAAS,EACT,GAAG,EACH,KAAK,EAKN;IACC,qBACE,sSAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QAC1D,OAAO;QACP,QAAQ;QACR,KAAK,IAAI,IAAI,KAAK,MAAM,GAAG;QAC3B,KAAK;QACL,SAAS,CAAC;YACR,EAAE,aAAa,CAAC,GAAG,GACjB;QACJ;;;;;;AAGN;KAtBgB", "debugId": null}}, {"offset": {"line": 4927, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\r\nimport { ChevronDownIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Accordion({\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\r\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />\r\n}\r\n\r\nfunction AccordionItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\r\n  return (\r\n    <AccordionPrimitive.Item\r\n      data-slot=\"accordion-item\"\r\n      className={cn(\"border-b last:border-b-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AccordionTrigger({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\r\n  return (\r\n    <AccordionPrimitive.Header className=\"flex\">\r\n      <AccordionPrimitive.Trigger\r\n        data-slot=\"accordion-trigger\"\r\n        className={cn(\r\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <ChevronDownIcon className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\r\n      </AccordionPrimitive.Trigger>\r\n    </AccordionPrimitive.Header>\r\n  )\r\n}\r\n\r\nfunction AccordionContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\r\n  return (\r\n    <AccordionPrimitive.Content\r\n      data-slot=\"accordion-content\"\r\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\r\n      {...props}\r\n    >\r\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\r\n    </AccordionPrimitive.Content>\r\n  )\r\n}\r\n\r\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,UAAU,EACjB,GAAG,OACkD;IACrD,qBAAO,sSAAC,+QAAA,CAAA,OAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;KAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,sSAAC,+QAAA,CAAA,OAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,sSAAC,+QAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,sSAAC,+QAAA,CAAA,UAA0B;YACzB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8SACA;YAED,GAAG,KAAK;;gBAER;8BACD,sSAAC,+SAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MApBS;AAsBT,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,sSAAC,+QAAA,CAAA,UAA0B;QACzB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;AAGnD;MAdS", "debugId": null}}, {"offset": {"line": 5029, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"~/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 5060, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/app/chat/components/research-activities-block.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { PythonOutlined } from \"@ant-design/icons\";\r\nimport { motion } from \"framer-motion\";\r\nimport { LRUCache } from \"lru-cache\";\r\nimport { BookOpenText, FileText, PencilRuler, Search } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { useMemo } from \"react\";\r\nimport SyntaxHighlighter from \"react-syntax-highlighter\";\r\nimport { docco } from \"react-syntax-highlighter/dist/esm/styles/hljs\";\r\nimport { dark } from \"react-syntax-highlighter/dist/esm/styles/prism\";\r\n\r\nimport { FavIcon } from \"~/components/deer-flow/fav-icon\";\r\nimport Image from \"~/components/deer-flow/image\";\r\nimport { LoadingAnimation } from \"~/components/deer-flow/loading-animation\";\r\nimport { Markdown } from \"~/components/deer-flow/markdown\";\r\nimport { RainbowText } from \"~/components/deer-flow/rainbow-text\";\r\nimport { Tooltip } from \"~/components/deer-flow/tooltip\";\r\nimport {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from \"~/components/ui/accordion\";\r\nimport { Skeleton } from \"~/components/ui/skeleton\";\r\nimport { findMCPTool } from \"~/core/mcp\";\r\nimport type { ToolCallRuntime } from \"~/core/messages\";\r\nimport { useMessage, useStore } from \"~/core/store\";\r\nimport { parseJSON } from \"~/core/utils\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport function ResearchActivitiesBlock({\r\n  className,\r\n  researchId,\r\n}: {\r\n  className?: string;\r\n  researchId: string;\r\n}) {\r\n  const activityIds = useStore((state) =>\r\n    state.researchActivityIds.get(researchId),\r\n  )!;\r\n  const ongoing = useStore((state) => state.ongoingResearchId === researchId);\r\n  return (\r\n    <>\r\n      <ul className={cn(\"flex flex-col py-4\", className)}>\r\n        {activityIds.map(\r\n          (activityId, i) =>\r\n            i !== 0 && (\r\n              <motion.li\r\n                key={activityId}\r\n                style={{ transition: \"all 0.4s ease-out\" }}\r\n                initial={{ opacity: 0, y: 24 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{\r\n                  duration: 0.4,\r\n                  ease: \"easeOut\",\r\n                }}\r\n              >\r\n                <ActivityMessage messageId={activityId} />\r\n                <ActivityListItem messageId={activityId} />\r\n                {i !== activityIds.length - 1 && <hr className=\"my-8\" />}\r\n              </motion.li>\r\n            ),\r\n        )}\r\n      </ul>\r\n      {ongoing && <LoadingAnimation className=\"mx-4 my-12\" />}\r\n    </>\r\n  );\r\n}\r\n\r\nfunction ActivityMessage({ messageId }: { messageId: string }) {\r\n  const message = useMessage(messageId);\r\n  if (message?.agent && message.content) {\r\n    if (message.agent !== \"reporter\" && message.agent !== \"planner\") {\r\n      return (\r\n        <div className=\"px-4 py-2\">\r\n          <Markdown animated checkLinkCredibility>\r\n            {message.content}\r\n          </Markdown>\r\n        </div>\r\n      );\r\n    }\r\n  }\r\n  return null;\r\n}\r\n\r\nfunction ActivityListItem({ messageId }: { messageId: string }) {\r\n  const message = useMessage(messageId);\r\n  if (message) {\r\n    if (!message.isStreaming && message.toolCalls?.length) {\r\n      for (const toolCall of message.toolCalls) {\r\n        if (toolCall.name === \"web_search\") {\r\n          return <WebSearchToolCall key={toolCall.id} toolCall={toolCall} />;\r\n        } else if (toolCall.name === \"crawl_tool\") {\r\n          return <CrawlToolCall key={toolCall.id} toolCall={toolCall} />;\r\n        } else if (toolCall.name === \"python_repl_tool\") {\r\n          return <PythonToolCall key={toolCall.id} toolCall={toolCall} />;\r\n        } else if (toolCall.name === \"local_search_tool\") {\r\n          return <RetrieverToolCall key={toolCall.id} toolCall={toolCall} />;\r\n        } else {\r\n          return <MCPToolCall key={toolCall.id} toolCall={toolCall} />;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  return null;\r\n}\r\n\r\nconst __pageCache = new LRUCache<string, string>({ max: 100 });\r\ntype SearchResult =\r\n  | {\r\n      type: \"page\";\r\n      title: string;\r\n      url: string;\r\n      content: string;\r\n    }\r\n  | {\r\n      type: \"image\";\r\n      image_url: string;\r\n      image_description: string;\r\n    };\r\n\r\nfunction WebSearchToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {\r\n  const searching = useMemo(() => {\r\n    return toolCall.result === undefined;\r\n  }, [toolCall.result]);\r\n  const searchResults = useMemo<SearchResult[]>(() => {\r\n    let results: SearchResult[] | undefined = undefined;\r\n    try {\r\n      results = toolCall.result ? parseJSON(toolCall.result, []) : undefined;\r\n    } catch {\r\n      results = undefined;\r\n    }\r\n    if (Array.isArray(results)) {\r\n      results.forEach((result) => {\r\n        if (result.type === \"page\") {\r\n          __pageCache.set(result.url, result.title);\r\n        }\r\n      });\r\n    } else {\r\n      results = [];\r\n    }\r\n    return results;\r\n  }, [toolCall.result]);\r\n  const pageResults = useMemo(\r\n    () => searchResults?.filter((result) => result.type === \"page\"),\r\n    [searchResults],\r\n  );\r\n  const imageResults = useMemo(\r\n    () => searchResults?.filter((result) => result.type === \"image\"),\r\n    [searchResults],\r\n  );\r\n  return (\r\n    <section className=\"mt-4 pl-4\">\r\n      <div className=\"font-medium italic\">\r\n        <RainbowText\r\n          className=\"flex items-center\"\r\n          animated={searchResults === undefined}\r\n        >\r\n          <Search size={16} className={\"mr-2\"} />\r\n          <span>Searching for&nbsp;</span>\r\n          <span className=\"max-w-[500px] overflow-hidden text-ellipsis whitespace-nowrap\">\r\n            {(toolCall.args as { query: string }).query}\r\n          </span>\r\n        </RainbowText>\r\n      </div>\r\n      <div className=\"pr-4\">\r\n        {pageResults && (\r\n          <ul className=\"mt-2 flex flex-wrap gap-4\">\r\n            {searching &&\r\n              [...Array(6)].map((_, i) => (\r\n                <li\r\n                  key={`search-result-${i}`}\r\n                  className=\"flex h-40 w-40 gap-2 rounded-md text-sm\"\r\n                >\r\n                  <Skeleton\r\n                    className=\"to-accent h-full w-full rounded-md bg-gradient-to-tl from-slate-400\"\r\n                    style={{ animationDelay: `${i * 0.2}s` }}\r\n                  />\r\n                </li>\r\n              ))}\r\n            {pageResults\r\n              .filter((result) => result.type === \"page\")\r\n              .map((searchResult, i) => (\r\n                <motion.li\r\n                  key={`search-result-${i}`}\r\n                  className=\"text-muted-foreground bg-accent flex max-w-40 gap-2 rounded-md px-2 py-1 text-sm\"\r\n                  initial={{ opacity: 0, y: 10, scale: 0.66 }}\r\n                  animate={{ opacity: 1, y: 0, scale: 1 }}\r\n                  transition={{\r\n                    duration: 0.2,\r\n                    delay: i * 0.1,\r\n                    ease: \"easeOut\",\r\n                  }}\r\n                >\r\n                  <FavIcon\r\n                    className=\"mt-1\"\r\n                    url={searchResult.url}\r\n                    title={searchResult.title}\r\n                  />\r\n                  <a href={searchResult.url} target=\"_blank\">\r\n                    {searchResult.title}\r\n                  </a>\r\n                </motion.li>\r\n              ))}\r\n            {imageResults.map((searchResult, i) => (\r\n              <motion.li\r\n                key={`search-result-${i}`}\r\n                initial={{ opacity: 0, y: 10, scale: 0.66 }}\r\n                animate={{ opacity: 1, y: 0, scale: 1 }}\r\n                transition={{\r\n                  duration: 0.2,\r\n                  delay: i * 0.1,\r\n                  ease: \"easeOut\",\r\n                }}\r\n              >\r\n                <a\r\n                  className=\"flex flex-col gap-2 overflow-hidden rounded-md opacity-75 transition-opacity duration-300 hover:opacity-100\"\r\n                  href={searchResult.image_url}\r\n                  target=\"_blank\"\r\n                >\r\n                  <Image\r\n                    src={searchResult.image_url}\r\n                    alt={searchResult.image_description}\r\n                    className=\"bg-accent h-40 w-40 max-w-full rounded-md bg-cover bg-center bg-no-repeat\"\r\n                    imageClassName=\"hover:scale-110\"\r\n                    imageTransition\r\n                  />\r\n                </a>\r\n              </motion.li>\r\n            ))}\r\n          </ul>\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nfunction CrawlToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {\r\n  const url = useMemo(\r\n    () => (toolCall.args as { url: string }).url,\r\n    [toolCall.args],\r\n  );\r\n  const title = useMemo(() => __pageCache.get(url), [url]);\r\n  return (\r\n    <section className=\"mt-4 pl-4\">\r\n      <div>\r\n        <RainbowText\r\n          className=\"flex items-center text-base font-medium italic\"\r\n          animated={toolCall.result === undefined}\r\n        >\r\n          <BookOpenText size={16} className={\"mr-2\"} />\r\n          <span>Reading</span>\r\n        </RainbowText>\r\n      </div>\r\n      <ul className=\"mt-2 flex flex-wrap gap-4\">\r\n        <motion.li\r\n          className=\"text-muted-foreground bg-accent flex h-40 w-40 gap-2 rounded-md px-2 py-1 text-sm\"\r\n          initial={{ opacity: 0, y: 10, scale: 0.66 }}\r\n          animate={{ opacity: 1, y: 0, scale: 1 }}\r\n          transition={{\r\n            duration: 0.2,\r\n            ease: \"easeOut\",\r\n          }}\r\n        >\r\n          <FavIcon className=\"mt-1\" url={url} title={title} />\r\n          <a\r\n            className=\"h-full flex-grow overflow-hidden text-ellipsis whitespace-nowrap\"\r\n            href={url}\r\n            target=\"_blank\"\r\n          >\r\n            {title ?? url}\r\n          </a>\r\n        </motion.li>\r\n      </ul>\r\n    </section>\r\n  );\r\n}\r\n\r\nfunction RetrieverToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {\r\n  const searching = useMemo(() => {\r\n    return toolCall.result === undefined;\r\n  }, [toolCall.result]);\r\n  const documents = useMemo<\r\n    Array<{ id: string; title: string; content: string }>\r\n  >(() => {\r\n    return toolCall.result ? parseJSON(toolCall.result, []) : [];\r\n  }, [toolCall.result]);\r\n  return (\r\n    <section className=\"mt-4 pl-4\">\r\n      <div className=\"font-medium italic\">\r\n        <RainbowText className=\"flex items-center\" animated={searching}>\r\n          <Search size={16} className={\"mr-2\"} />\r\n          <span>Retrieving documents from RAG&nbsp;</span>\r\n          <span className=\"max-w-[500px] overflow-hidden text-ellipsis whitespace-nowrap\">\r\n            {(toolCall.args as { keywords: string }).keywords}\r\n          </span>\r\n        </RainbowText>\r\n      </div>\r\n      <div className=\"pr-4\">\r\n        {documents && (\r\n          <ul className=\"mt-2 flex flex-wrap gap-4\">\r\n            {searching &&\r\n              [...Array(2)].map((_, i) => (\r\n                <li\r\n                  key={`search-result-${i}`}\r\n                  className=\"flex h-40 w-40 gap-2 rounded-md text-sm\"\r\n                >\r\n                  <Skeleton\r\n                    className=\"to-accent h-full w-full rounded-md bg-gradient-to-tl from-slate-400\"\r\n                    style={{ animationDelay: `${i * 0.2}s` }}\r\n                  />\r\n                </li>\r\n              ))}\r\n            {documents.map((doc, i) => (\r\n              <motion.li\r\n                key={`search-result-${i}`}\r\n                className=\"text-muted-foreground bg-accent flex max-w-40 gap-2 rounded-md px-2 py-1 text-sm\"\r\n                initial={{ opacity: 0, y: 10, scale: 0.66 }}\r\n                animate={{ opacity: 1, y: 0, scale: 1 }}\r\n                transition={{\r\n                  duration: 0.2,\r\n                  delay: i * 0.1,\r\n                  ease: \"easeOut\",\r\n                }}\r\n              >\r\n                <FileText size={32} />\r\n                {doc.title}\r\n              </motion.li>\r\n            ))}\r\n          </ul>\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nfunction PythonToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {\r\n  const code = useMemo<string | undefined>(() => {\r\n    return (toolCall.args as { code?: string }).code;\r\n  }, [toolCall.args]);\r\n  const { resolvedTheme } = useTheme();\r\n  return (\r\n    <section className=\"mt-4 pl-4\">\r\n      <div className=\"flex items-center\">\r\n        <PythonOutlined className={\"mr-2\"} />\r\n        <RainbowText\r\n          className=\"text-base font-medium italic\"\r\n          animated={toolCall.result === undefined}\r\n        >\r\n          Running Python code\r\n        </RainbowText>\r\n      </div>\r\n      <div>\r\n        <div className=\"bg-accent mt-2 max-h-[400px] max-w-[calc(100%-120px)] overflow-y-auto rounded-md p-2 text-sm\">\r\n          <SyntaxHighlighter\r\n            language=\"python\"\r\n            style={resolvedTheme === \"dark\" ? dark : docco}\r\n            customStyle={{\r\n              background: \"transparent\",\r\n              border: \"none\",\r\n              boxShadow: \"none\",\r\n            }}\r\n          >\r\n            {code?.trim() ?? \"\"}\r\n          </SyntaxHighlighter>\r\n        </div>\r\n      </div>\r\n      {toolCall.result && <PythonToolCallResult result={toolCall.result} />}\r\n    </section>\r\n  );\r\n}\r\n\r\nfunction PythonToolCallResult({ result }: { result: string }) {\r\n  const { resolvedTheme } = useTheme();\r\n  const hasError = useMemo(\r\n    () => result.includes(\"Error executing code:\\n\"),\r\n    [result],\r\n  );\r\n  const error = useMemo(() => {\r\n    if (hasError) {\r\n      const parts = result.split(\"```\\nError: \");\r\n      if (parts.length > 1) {\r\n        return parts[1]!.trim();\r\n      }\r\n    }\r\n    return null;\r\n  }, [result, hasError]);\r\n  const stdout = useMemo(() => {\r\n    if (!hasError) {\r\n      const parts = result.split(\"```\\nStdout: \");\r\n      if (parts.length > 1) {\r\n        return parts[1]!.trim();\r\n      }\r\n    }\r\n    return null;\r\n  }, [result, hasError]);\r\n  return (\r\n    <>\r\n      <div className=\"mt-4 font-medium italic\">\r\n        {hasError ? \"Error when executing the above code\" : \"Execution output\"}\r\n      </div>\r\n      <div className=\"bg-accent mt-2 max-h-[400px] max-w-[calc(100%-120px)] overflow-y-auto rounded-md p-2 text-sm\">\r\n        <SyntaxHighlighter\r\n          language=\"plaintext\"\r\n          style={resolvedTheme === \"dark\" ? dark : docco}\r\n          customStyle={{\r\n            color: hasError ? \"red\" : \"inherit\",\r\n            background: \"transparent\",\r\n            border: \"none\",\r\n            boxShadow: \"none\",\r\n          }}\r\n        >\r\n          {error ?? stdout ?? \"(empty)\"}\r\n        </SyntaxHighlighter>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n\r\nfunction MCPToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {\r\n  const tool = useMemo(() => findMCPTool(toolCall.name), [toolCall.name]);\r\n  const { resolvedTheme } = useTheme();\r\n  return (\r\n    <section className=\"mt-4 pl-4\">\r\n      <div className=\"w-fit overflow-y-auto rounded-md py-0\">\r\n        <Accordion type=\"single\" collapsible className=\"w-full\">\r\n          <AccordionItem value=\"item-1\">\r\n            <AccordionTrigger>\r\n              <Tooltip title={tool?.description}>\r\n                <div className=\"flex items-center font-medium italic\">\r\n                  <PencilRuler size={16} className={\"mr-2\"} />\r\n                  <RainbowText\r\n                    className=\"pr-0.5 text-base font-medium italic\"\r\n                    animated={toolCall.result === undefined}\r\n                  >\r\n                    Running {toolCall.name ? toolCall.name + \"()\" : \"MCP tool\"}\r\n                  </RainbowText>\r\n                </div>\r\n              </Tooltip>\r\n            </AccordionTrigger>\r\n            <AccordionContent>\r\n              {toolCall.result && (\r\n                <div className=\"bg-accent max-h-[400px] max-w-[560px] overflow-y-auto rounded-md text-sm\">\r\n                  <SyntaxHighlighter\r\n                    language=\"json\"\r\n                    style={resolvedTheme === \"dark\" ? dark : docco}\r\n                    customStyle={{\r\n                      background: \"transparent\",\r\n                      border: \"none\",\r\n                      boxShadow: \"none\",\r\n                    }}\r\n                  >\r\n                    {toolCall.result.trim()}\r\n                  </SyntaxHighlighter>\r\n                </div>\r\n              )}\r\n            </AccordionContent>\r\n          </AccordionItem>\r\n        </Accordion>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAAA;AAEA;AAAA;AACA;AAAA;AACA;;;;;;;;;;;;;;;;;;;;;;;;AAEO,SAAS,wBAAwB,EACtC,SAAS,EACT,UAAU,EAIX;;IACC,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;yDAAE,CAAC,QAC5B,MAAM,mBAAmB,CAAC,GAAG,CAAC;;IAEhC,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;qDAAE,CAAC,QAAU,MAAM,iBAAiB,KAAK;;IAChE,qBACE;;0BACE,sSAAC;gBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;0BACrC,YAAY,GAAG,CACd,CAAC,YAAY,IACX,MAAM,mBACJ,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;wBAER,OAAO;4BAAE,YAAY;wBAAoB;wBACzC,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BACV,UAAU;4BACV,MAAM;wBACR;;0CAEA,sSAAC;gCAAgB,WAAW;;;;;;0CAC5B,sSAAC;gCAAiB,WAAW;;;;;;4BAC5B,MAAM,YAAY,MAAM,GAAG,mBAAK,sSAAC;gCAAG,WAAU;;;;;;;uBAX1C;;;;;;;;;;YAgBd,yBAAW,sSAAC,6JAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;AAG9C;GArCgB;;QAOM,gIAAA,CAAA,WAAQ;QAGZ,gIAAA,CAAA,WAAQ;;;KAVV;AAuChB,SAAS,gBAAgB,EAAE,SAAS,EAAyB;;IAC3D,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,SAAS,SAAS,QAAQ,OAAO,EAAE;QACrC,IAAI,QAAQ,KAAK,KAAK,cAAc,QAAQ,KAAK,KAAK,WAAW;YAC/D,qBACE,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,iJAAA,CAAA,WAAQ;oBAAC,QAAQ;oBAAC,oBAAoB;8BACpC,QAAQ,OAAO;;;;;;;;;;;QAIxB;IACF;IACA,OAAO;AACT;IAdS;;QACS,gIAAA,CAAA,aAAU;;;MADnB;AAgBT,SAAS,iBAAiB,EAAE,SAAS,EAAyB;;IAC5D,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,SAAS;QACX,IAAI,CAAC,QAAQ,WAAW,IAAI,QAAQ,SAAS,EAAE,QAAQ;YACrD,KAAK,MAAM,YAAY,QAAQ,SAAS,CAAE;gBACxC,IAAI,SAAS,IAAI,KAAK,cAAc;oBAClC,qBAAO,sSAAC;wBAAoC,UAAU;uBAAvB,SAAS,EAAE;;;;;gBAC5C,OAAO,IAAI,SAAS,IAAI,KAAK,cAAc;oBACzC,qBAAO,sSAAC;wBAAgC,UAAU;uBAAvB,SAAS,EAAE;;;;;gBACxC,OAAO,IAAI,SAAS,IAAI,KAAK,oBAAoB;oBAC/C,qBAAO,sSAAC;wBAAiC,UAAU;uBAAvB,SAAS,EAAE;;;;;gBACzC,OAAO,IAAI,SAAS,IAAI,KAAK,qBAAqB;oBAChD,qBAAO,sSAAC;wBAAoC,UAAU;uBAAvB,SAAS,EAAE;;;;;gBAC5C,OAAO;oBACL,qBAAO,sSAAC;wBAA8B,UAAU;uBAAvB,SAAS,EAAE;;;;;gBACtC;YACF;QACF;IACF;IACA,OAAO;AACT;IApBS;;QACS,gIAAA,CAAA,aAAU;;;MADnB;AAsBT,MAAM,cAAc,IAAI,iNAAA,CAAA,WAAQ,CAAiB;IAAE,KAAK;AAAI;AAc5D,SAAS,kBAAkB,EAAE,QAAQ,EAAiC;;IACpE,MAAM,YAAY,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;gDAAE;YACxB,OAAO,SAAS,MAAM,KAAK;QAC7B;+CAAG;QAAC,SAAS,MAAM;KAAC;IACpB,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;oDAAkB;YAC5C,IAAI,UAAsC;YAC1C,IAAI;gBACF,UAAU,SAAS,MAAM,GAAG,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD,EAAE,SAAS,MAAM,EAAE,EAAE,IAAI;YAC/D,EAAE,OAAM;gBACN,UAAU;YACZ;YACA,IAAI,MAAM,OAAO,CAAC,UAAU;gBAC1B,QAAQ,OAAO;gEAAC,CAAC;wBACf,IAAI,OAAO,IAAI,KAAK,QAAQ;4BAC1B,YAAY,GAAG,CAAC,OAAO,GAAG,EAAE,OAAO,KAAK;wBAC1C;oBACF;;YACF,OAAO;gBACL,UAAU,EAAE;YACd;YACA,OAAO;QACT;mDAAG;QAAC,SAAS,MAAM;KAAC;IACpB,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;kDACxB,IAAM,eAAe;0DAAO,CAAC,SAAW,OAAO,IAAI,KAAK;;iDACxD;QAAC;KAAc;IAEjB,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;mDACzB,IAAM,eAAe;2DAAO,CAAC,SAAW,OAAO,IAAI,KAAK;;kDACxD;QAAC;KAAc;IAEjB,qBACE,sSAAC;QAAQ,WAAU;;0BACjB,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,wJAAA,CAAA,cAAW;oBACV,WAAU;oBACV,UAAU,kBAAkB;;sCAE5B,sSAAC,6RAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAW;;;;;;sCAC7B,sSAAC;sCAAK;;;;;;sCACN,sSAAC;4BAAK,WAAU;sCACb,AAAC,SAAS,IAAI,CAAuB,KAAK;;;;;;;;;;;;;;;;;0BAIjD,sSAAC;gBAAI,WAAU;0BACZ,6BACC,sSAAC;oBAAG,WAAU;;wBACX,aACC;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACpB,sSAAC;gCAEC,WAAU;0CAEV,cAAA,sSAAC,uIAAA,CAAA,WAAQ;oCACP,WAAU;oCACV,OAAO;wCAAE,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;oCAAC;;;;;;+BALpC,CAAC,cAAc,EAAE,GAAG;;;;;wBAS9B,YACE,MAAM,CAAC,CAAC,SAAW,OAAO,IAAI,KAAK,QACnC,GAAG,CAAC,CAAC,cAAc,kBAClB,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;oCAAI,OAAO;gCAAK;gCAC1C,SAAS;oCAAE,SAAS;oCAAG,GAAG;oCAAG,OAAO;gCAAE;gCACtC,YAAY;oCACV,UAAU;oCACV,OAAO,IAAI;oCACX,MAAM;gCACR;;kDAEA,sSAAC,oJAAA,CAAA,UAAO;wCACN,WAAU;wCACV,KAAK,aAAa,GAAG;wCACrB,OAAO,aAAa,KAAK;;;;;;kDAE3B,sSAAC;wCAAE,MAAM,aAAa,GAAG;wCAAE,QAAO;kDAC/B,aAAa,KAAK;;;;;;;+BAhBhB,CAAC,cAAc,EAAE,GAAG;;;;;wBAoB9B,aAAa,GAAG,CAAC,CAAC,cAAc,kBAC/B,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;gCAER,SAAS;oCAAE,SAAS;oCAAG,GAAG;oCAAI,OAAO;gCAAK;gCAC1C,SAAS;oCAAE,SAAS;oCAAG,GAAG;oCAAG,OAAO;gCAAE;gCACtC,YAAY;oCACV,UAAU;oCACV,OAAO,IAAI;oCACX,MAAM;gCACR;0CAEA,cAAA,sSAAC;oCACC,WAAU;oCACV,MAAM,aAAa,SAAS;oCAC5B,QAAO;8CAEP,cAAA,sSAAC,8IAAA,CAAA,UAAK;wCACJ,KAAK,aAAa,SAAS;wCAC3B,KAAK,aAAa,iBAAiB;wCACnC,WAAU;wCACV,gBAAe;wCACf,eAAe;;;;;;;;;;;+BAnBd,CAAC,cAAc,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;;AA6BzC;IAlHS;MAAA;AAoHT,SAAS,cAAc,EAAE,QAAQ,EAAiC;;IAChE,MAAM,MAAM,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;sCAChB,IAAM,AAAC,SAAS,IAAI,CAAqB,GAAG;qCAC5C;QAAC,SAAS,IAAI;KAAC;IAEjB,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;wCAAE,IAAM,YAAY,GAAG,CAAC;uCAAM;QAAC;KAAI;IACvD,qBACE,sSAAC;QAAQ,WAAU;;0BACjB,sSAAC;0BACC,cAAA,sSAAC,wJAAA,CAAA,cAAW;oBACV,WAAU;oBACV,UAAU,SAAS,MAAM,KAAK;;sCAE9B,sSAAC,iTAAA,CAAA,eAAY;4BAAC,MAAM;4BAAI,WAAW;;;;;;sCACnC,sSAAC;sCAAK;;;;;;;;;;;;;;;;;0BAGV,sSAAC;gBAAG,WAAU;0BACZ,cAAA,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;oBACR,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;wBAAI,OAAO;oBAAK;oBAC1C,SAAS;wBAAE,SAAS;wBAAG,GAAG;wBAAG,OAAO;oBAAE;oBACtC,YAAY;wBACV,UAAU;wBACV,MAAM;oBACR;;sCAEA,sSAAC,oJAAA,CAAA,UAAO;4BAAC,WAAU;4BAAO,KAAK;4BAAK,OAAO;;;;;;sCAC3C,sSAAC;4BACC,WAAU;4BACV,MAAM;4BACN,QAAO;sCAEN,SAAS;;;;;;;;;;;;;;;;;;;;;;;AAMtB;IAvCS;MAAA;AAyCT,SAAS,kBAAkB,EAAE,QAAQ,EAAiC;;IACpE,MAAM,YAAY,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;gDAAE;YACxB,OAAO,SAAS,MAAM,KAAK;QAC7B;+CAAG;QAAC,SAAS,MAAM;KAAC;IACpB,MAAM,YAAY,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;gDAEtB;YACA,OAAO,SAAS,MAAM,GAAG,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD,EAAE,SAAS,MAAM,EAAE,EAAE,IAAI,EAAE;QAC9D;+CAAG;QAAC,SAAS,MAAM;KAAC;IACpB,qBACE,sSAAC;QAAQ,WAAU;;0BACjB,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,wJAAA,CAAA,cAAW;oBAAC,WAAU;oBAAoB,UAAU;;sCACnD,sSAAC,6RAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAW;;;;;;sCAC7B,sSAAC;sCAAK;;;;;;sCACN,sSAAC;4BAAK,WAAU;sCACb,AAAC,SAAS,IAAI,CAA0B,QAAQ;;;;;;;;;;;;;;;;;0BAIvD,sSAAC;gBAAI,WAAU;0BACZ,2BACC,sSAAC;oBAAG,WAAU;;wBACX,aACC;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACpB,sSAAC;gCAEC,WAAU;0CAEV,cAAA,sSAAC,uIAAA,CAAA,WAAQ;oCACP,WAAU;oCACV,OAAO;wCAAE,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;oCAAC;;;;;;+BALpC,CAAC,cAAc,EAAE,GAAG;;;;;wBAS9B,UAAU,GAAG,CAAC,CAAC,KAAK,kBACnB,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;oCAAI,OAAO;gCAAK;gCAC1C,SAAS;oCAAE,SAAS;oCAAG,GAAG;oCAAG,OAAO;gCAAE;gCACtC,YAAY;oCACV,UAAU;oCACV,OAAO,IAAI;oCACX,MAAM;gCACR;;kDAEA,sSAAC,qSAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCACf,IAAI,KAAK;;+BAXL,CAAC,cAAc,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;;AAmBzC;IAxDS;MAAA;AA0DT,SAAS,eAAe,EAAE,QAAQ,EAAiC;;IACjE,MAAM,OAAO,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;wCAAsB;YACvC,OAAO,AAAC,SAAS,IAAI,CAAuB,IAAI;QAClD;uCAAG;QAAC,SAAS,IAAI;KAAC;IAClB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IACjC,qBACE,sSAAC;QAAQ,WAAU;;0BACjB,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,mUAAA,CAAA,iBAAc;wBAAC,WAAW;;;;;;kCAC3B,sSAAC,wJAAA,CAAA,cAAW;wBACV,WAAU;wBACV,UAAU,SAAS,MAAM,KAAK;kCAC/B;;;;;;;;;;;;0BAIH,sSAAC;0BACC,cAAA,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC,0RAAA,CAAA,UAAiB;wBAChB,UAAS;wBACT,OAAO,kBAAkB,SAAS,gUAAA,CAAA,OAAI,GAAG,iUAAA,CAAA,QAAK;wBAC9C,aAAa;4BACX,YAAY;4BACZ,QAAQ;4BACR,WAAW;wBACb;kCAEC,MAAM,UAAU;;;;;;;;;;;;;;;;YAItB,SAAS,MAAM,kBAAI,sSAAC;gBAAqB,QAAQ,SAAS,MAAM;;;;;;;;;;;;AAGvE;IAlCS;;QAImB,4PAAA,CAAA,WAAQ;;;MAJ3B;AAoCT,SAAS,qBAAqB,EAAE,MAAM,EAAsB;;IAC1D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;kDACrB,IAAM,OAAO,QAAQ,CAAC;iDACtB;QAAC;KAAO;IAEV,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;+CAAE;YACpB,IAAI,UAAU;gBACZ,MAAM,QAAQ,OAAO,KAAK,CAAC;gBAC3B,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,OAAO,KAAK,CAAC,EAAE,CAAE,IAAI;gBACvB;YACF;YACA,OAAO;QACT;8CAAG;QAAC;QAAQ;KAAS;IACrB,MAAM,SAAS,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;gDAAE;YACrB,IAAI,CAAC,UAAU;gBACb,MAAM,QAAQ,OAAO,KAAK,CAAC;gBAC3B,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,OAAO,KAAK,CAAC,EAAE,CAAE,IAAI;gBACvB;YACF;YACA,OAAO;QACT;+CAAG;QAAC;QAAQ;KAAS;IACrB,qBACE;;0BACE,sSAAC;gBAAI,WAAU;0BACZ,WAAW,wCAAwC;;;;;;0BAEtD,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,0RAAA,CAAA,UAAiB;oBAChB,UAAS;oBACT,OAAO,kBAAkB,SAAS,gUAAA,CAAA,OAAI,GAAG,iUAAA,CAAA,QAAK;oBAC9C,aAAa;wBACX,OAAO,WAAW,QAAQ;wBAC1B,YAAY;wBACZ,QAAQ;wBACR,WAAW;oBACb;8BAEC,SAAS,UAAU;;;;;;;;;;;;;AAK9B;IA7CS;;QACmB,4PAAA,CAAA,WAAQ;;;MAD3B;AA+CT,SAAS,YAAY,EAAE,QAAQ,EAAiC;;IAC9D,MAAM,OAAO,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;qCAAE,IAAM,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,SAAS,IAAI;oCAAG;QAAC,SAAS,IAAI;KAAC;IACtE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IACjC,qBACE,sSAAC;QAAQ,WAAU;kBACjB,cAAA,sSAAC;YAAI,WAAU;sBACb,cAAA,sSAAC,wIAAA,CAAA,YAAS;gBAAC,MAAK;gBAAS,WAAW;gBAAC,WAAU;0BAC7C,cAAA,sSAAC,wIAAA,CAAA,gBAAa;oBAAC,OAAM;;sCACnB,sSAAC,wIAAA,CAAA,mBAAgB;sCACf,cAAA,sSAAC,gJAAA,CAAA,UAAO;gCAAC,OAAO,MAAM;0CACpB,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,2SAAA,CAAA,cAAW;4CAAC,MAAM;4CAAI,WAAW;;;;;;sDAClC,sSAAC,wJAAA,CAAA,cAAW;4CACV,WAAU;4CACV,UAAU,SAAS,MAAM,KAAK;;gDAC/B;gDACU,SAAS,IAAI,GAAG,SAAS,IAAI,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;sCAKxD,sSAAC,wIAAA,CAAA,mBAAgB;sCACd,SAAS,MAAM,kBACd,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC,0RAAA,CAAA,UAAiB;oCAChB,UAAS;oCACT,OAAO,kBAAkB,SAAS,gUAAA,CAAA,OAAI,GAAG,iUAAA,CAAA,QAAK;oCAC9C,aAAa;wCACX,YAAY;wCACZ,QAAQ;wCACR,WAAW;oCACb;8CAEC,SAAS,MAAM,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzC;IA3CS;;QAEmB,4PAAA,CAAA,WAAQ;;;MAF3B", "debugId": null}}, {"offset": {"line": 6039, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/editor/extensions.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport {\r\n  AIHighlight,\r\n  CharacterCount,\r\n  CodeBlockLowlight,\r\n  Color,\r\n  CustomKeymap,\r\n  GlobalDragHandle,\r\n  HighlightExtension,\r\n  HorizontalRule,\r\n  Mathematics,\r\n  Placeholder,\r\n  StarterKit,\r\n  TaskItem,\r\n  TaskList,\r\n  TextStyle,\r\n  TiptapImage,\r\n  TiptapLink,\r\n  TiptapUnderline,\r\n  Twitter,\r\n  UpdatedImage,\r\n  UploadImagesPlugin,\r\n  Youtube,\r\n} from \"novel\";\r\nimport { Markdown } from \"tiptap-markdown\";\r\nimport { Table } from \"@tiptap/extension-table\";\r\nimport { TableHeader } from \"@tiptap/extension-table-header\";\r\nimport { TableRow } from \"@tiptap/extension-table-row\";\r\nimport { TableCell } from \"@tiptap/extension-table-cell\";\r\nimport { cx } from \"class-variance-authority\";\r\nimport { common, createLowlight } from \"lowlight\";\r\n\r\n//TODO I am using cx here to get tailwind autocomplete working, idk if someone else can write a regex to just capture the class key in objects\r\nconst aiHighlight = AIHighlight;\r\n//You can overwrite the placeholder with your own configuration\r\nconst placeholder = Placeholder;\r\nconst tiptapLink = TiptapLink.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\r\n      \"text-muted-foreground underline underline-offset-[3px] hover:text-primary transition-colors cursor-pointer\",\r\n    ),\r\n  },\r\n});\r\n\r\nconst tiptapImage = TiptapImage.extend({\r\n  addProseMirrorPlugins() {\r\n    return [\r\n      UploadImagesPlugin({\r\n        imageClass: cx(\"opacity-40 rounded-lg border border-stone-200\"),\r\n      }),\r\n    ];\r\n  },\r\n}).configure({\r\n  allowBase64: true,\r\n  HTMLAttributes: {\r\n    class: cx(\"rounded-lg border border-muted\"),\r\n  },\r\n});\r\n\r\nconst updatedImage = UpdatedImage.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"rounded-lg border border-muted\"),\r\n  },\r\n});\r\n\r\nconst taskList = TaskList.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"not-prose pl-2 \"),\r\n  },\r\n});\r\nconst taskItem = TaskItem.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"flex gap-2 items-start my-4\"),\r\n  },\r\n  nested: true,\r\n});\r\n\r\nconst horizontalRule = HorizontalRule.configure({\r\n  HTMLAttributes: {},\r\n});\r\n\r\nconst starterKit = StarterKit.configure({\r\n  bulletList: {\r\n    HTMLAttributes: {},\r\n  },\r\n  orderedList: {\r\n    HTMLAttributes: {\r\n      class: cx(\"list-decimal list-outside leading-3 -mt-2\"),\r\n    },\r\n  },\r\n  listItem: {\r\n    HTMLAttributes: {},\r\n  },\r\n  blockquote: {\r\n    HTMLAttributes: {\r\n      class: cx(\"border-l-4 border-primary\"),\r\n    },\r\n  },\r\n  codeBlock: false,\r\n  code: {\r\n    HTMLAttributes: {\r\n      spellcheck: \"false\",\r\n    },\r\n  },\r\n  horizontalRule: false,\r\n  dropcursor: {\r\n    color: \"#DBEAFE\",\r\n    width: 4,\r\n  },\r\n  gapcursor: false,\r\n});\r\n\r\nconst codeBlockLowlight = CodeBlockLowlight.configure({\r\n  // configure lowlight: common /  all / use highlightJS in case there is a need to specify certain language grammars only\r\n  // common: covers 37 language grammars which should be good enough in most cases\r\n  lowlight: createLowlight(common),\r\n});\r\n\r\nconst youtube = Youtube.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"rounded-lg border border-muted\"),\r\n  },\r\n  inline: false,\r\n});\r\n\r\nconst twitter = Twitter.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"not-prose\"),\r\n  },\r\n  inline: false,\r\n});\r\n\r\nconst mathematics = Mathematics.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"text-foreground rounded p-1 hover:bg-accent cursor-pointer\"),\r\n  },\r\n  katexOptions: {\r\n    throwOnError: false,\r\n  },\r\n});\r\n\r\nconst characterCount = CharacterCount.configure();\r\n\r\nconst table = Table.configure();\r\nconst tableRow = TableRow.configure();\r\nconst tableCell = TableCell.configure();\r\nconst tableHeader = TableHeader.configure();\r\n\r\nconst markdownExtension = Markdown.configure({\r\n  html: true,\r\n  tightLists: true,\r\n  tightListClass: \"tight\",\r\n  bulletListMarker: \"-\",\r\n  linkify: false,\r\n  breaks: false,\r\n  transformPastedText: false,\r\n  transformCopiedText: false,\r\n});\r\n\r\nconst globalDragHandle = GlobalDragHandle.configure({});\r\n\r\nexport const defaultExtensions = [\r\n  starterKit,\r\n  placeholder,\r\n  tiptapLink,\r\n  updatedImage,\r\n  taskList,\r\n  taskItem,\r\n  table,\r\n  tableRow,\r\n  tableCell,\r\n  tableHeader,\r\n  horizontalRule,\r\n  aiHighlight,\r\n  codeBlockLowlight,\r\n  youtube,\r\n  twitter,\r\n  mathematics,\r\n  characterCount,\r\n  TiptapUnderline,\r\n  markdownExtension,\r\n  HighlightExtension,\r\n  TextStyle,\r\n  Color,\r\n  CustomKeymap,\r\n  globalDragHandle,\r\n];\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;AAEA,8IAA8I;AAC9I,MAAM,cAAc,kQAAA,CAAA,cAAW;AAC/B,+DAA+D;AAC/D,MAAM,cAAc,kQAAA,CAAA,cAAW;AAC/B,MAAM,aAAa,iTAAA,CAAA,aAAU,CAAC,SAAS,CAAC;IACtC,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EACN;IAEJ;AACF;AAEA,MAAM,cAAc,mTAAA,CAAA,cAAW,CAAC,MAAM,CAAC;IACrC;QACE,OAAO;YACL,CAAA,GAAA,kQAAA,CAAA,qBAAkB,AAAD,EAAE;gBACjB,YAAY,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;YACjB;SACD;IACH;AACF,GAAG,SAAS,CAAC;IACX,aAAa;IACb,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;IACZ;AACF;AAEA,MAAM,eAAe,kQAAA,CAAA,eAAY,CAAC,SAAS,CAAC;IAC1C,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;IACZ;AACF;AAEA,MAAM,WAAW,6QAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;IAClC,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;IACZ;AACF;AACA,MAAM,WAAW,6QAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;IAClC,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;IACZ;IACA,QAAQ;AACV;AAEA,MAAM,iBAAiB,kQAAA,CAAA,iBAAc,CAAC,SAAS,CAAC;IAC9C,gBAAgB,CAAC;AACnB;AAEA,MAAM,aAAa,+QAAA,CAAA,aAAU,CAAC,SAAS,CAAC;IACtC,YAAY;QACV,gBAAgB,CAAC;IACnB;IACA,aAAa;QACX,gBAAgB;YACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;QACZ;IACF;IACA,UAAU;QACR,gBAAgB,CAAC;IACnB;IACA,YAAY;QACV,gBAAgB;YACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;QACZ;IACF;IACA,WAAW;IACX,MAAM;QACJ,gBAAgB;YACd,YAAY;QACd;IACF;IACA,gBAAgB;IAChB,YAAY;QACV,OAAO;QACP,OAAO;IACT;IACA,WAAW;AACb;AAEA,MAAM,oBAAoB,0UAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC;IACpD,wHAAwH;IACxH,gFAAgF;IAChF,UAAU,CAAA,GAAA,gMAAA,CAAA,iBAAc,AAAD,EAAE,uOAAA,CAAA,SAAM;AACjC;AAEA,MAAM,UAAU,8SAAA,CAAA,UAAO,CAAC,SAAS,CAAC;IAChC,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;IACZ;IACA,QAAQ;AACV;AAEA,MAAM,UAAU,kQAAA,CAAA,UAAO,CAAC,SAAS,CAAC;IAChC,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;IACZ;IACA,QAAQ;AACV;AAEA,MAAM,cAAc,kQAAA,CAAA,cAAW,CAAC,SAAS,CAAC;IACxC,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;IACZ;IACA,cAAc;QACZ,cAAc;IAChB;AACF;AAEA,MAAM,iBAAiB,6TAAA,CAAA,iBAAc,CAAC,SAAS;AAE/C,MAAM,QAAQ,yQAAA,CAAA,QAAK,CAAC,SAAS;AAC7B,MAAM,WAAW,6QAAA,CAAA,WAAQ,CAAC,SAAS;AACnC,MAAM,YAAY,8QAAA,CAAA,YAAS,CAAC,SAAS;AACrC,MAAM,cAAc,gRAAA,CAAA,cAAW,CAAC,SAAS;AAEzC,MAAM,oBAAoB,kRAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;IAC3C,MAAM;IACN,YAAY;IACZ,gBAAgB;IAChB,kBAAkB;IAClB,SAAS;IACT,QAAQ;IACR,qBAAqB;IACrB,qBAAqB;AACvB;AAEA,MAAM,mBAAmB,+TAAA,CAAA,mBAAgB,CAAC,SAAS,CAAC,CAAC;AAE9C,MAAM,oBAAoB;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,qTAAA,CAAA,kBAAe;IACf;IACA,kQAAA,CAAA,qBAAkB;IAClB,sTAAA,CAAA,YAAS;IACT,yQAAA,CAAA,QAAK;IACL,kQAAA,CAAA,eAAY;IACZ;CACD", "debugId": null}}, {"offset": {"line": 6221, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,sSAAC,gRAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,sSAAC,gRAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,sSAAC,gRAAA,CAAA,SAAuB;kBACtB,cAAA,sSAAC,gRAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,sSAAC,gRAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 6302, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/editor/selectors/color-selector.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Check, ChevronDown } from \"lucide-react\";\r\nimport { EditorBubbleItem, useEditor } from \"novel\";\r\n\r\nimport { Button } from \"../../ui/button\";\r\nimport { Popover, PopoverContent, PopoverTrigger } from \"../../ui/popover\";\r\nexport interface BubbleColorMenuItem {\r\n  name: string;\r\n  color: string;\r\n}\r\n\r\nconst TEXT_COLORS: BubbleColorMenuItem[] = [\r\n  {\r\n    name: \"Default\",\r\n    color: \"var(--novel-black)\",\r\n  },\r\n  {\r\n    name: \"Purple\",\r\n    color: \"#9333EA\",\r\n  },\r\n  {\r\n    name: \"Red\",\r\n    color: \"#E00000\",\r\n  },\r\n  {\r\n    name: \"Yellow\",\r\n    color: \"#EAB308\",\r\n  },\r\n  {\r\n    name: \"Blue\",\r\n    color: \"#2563EB\",\r\n  },\r\n  {\r\n    name: \"Green\",\r\n    color: \"#008A00\",\r\n  },\r\n  {\r\n    name: \"Orange\",\r\n    color: \"#FFA500\",\r\n  },\r\n  {\r\n    name: \"<PERSON>\",\r\n    color: \"#BA4081\",\r\n  },\r\n  {\r\n    name: \"<PERSON>\",\r\n    color: \"#A8A29E\",\r\n  },\r\n];\r\n\r\nconst HIGHLIGHT_COLORS: BubbleColorMenuItem[] = [\r\n  {\r\n    name: \"Default\",\r\n    color: \"var(--novel-highlight-default)\",\r\n  },\r\n  {\r\n    name: \"Purple\",\r\n    color: \"var(--novel-highlight-purple)\",\r\n  },\r\n  {\r\n    name: \"Red\",\r\n    color: \"var(--novel-highlight-red)\",\r\n  },\r\n  {\r\n    name: \"Yellow\",\r\n    color: \"var(--novel-highlight-yellow)\",\r\n  },\r\n  {\r\n    name: \"Blue\",\r\n    color: \"var(--novel-highlight-blue)\",\r\n  },\r\n  {\r\n    name: \"Green\",\r\n    color: \"var(--novel-highlight-green)\",\r\n  },\r\n  {\r\n    name: \"Orange\",\r\n    color: \"var(--novel-highlight-orange)\",\r\n  },\r\n  {\r\n    name: \"Pink\",\r\n    color: \"var(--novel-highlight-pink)\",\r\n  },\r\n  {\r\n    name: \"Gray\",\r\n    color: \"var(--novel-highlight-gray)\",\r\n  },\r\n];\r\n\r\ninterface ColorSelectorProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n}\r\n\r\nexport const ColorSelector = ({ open, onOpenChange }: ColorSelectorProps) => {\r\n  const { editor } = useEditor();\r\n\r\n  if (!editor) return null;\r\n  const activeColorItem = TEXT_COLORS.find(({ color }) =>\r\n    editor.isActive(\"textStyle\", { color }),\r\n  );\r\n\r\n  const activeHighlightItem = HIGHLIGHT_COLORS.find(({ color }) =>\r\n    editor.isActive(\"highlight\", { color }),\r\n  );\r\n\r\n  return (\r\n    <Popover modal={true} open={open} onOpenChange={onOpenChange}>\r\n      <PopoverTrigger asChild>\r\n        <Button size=\"sm\" className=\"gap-2 rounded-none\" variant=\"ghost\">\r\n          <span\r\n            className=\"rounded-sm px-1\"\r\n            style={{\r\n              color: activeColorItem?.color,\r\n              backgroundColor: activeHighlightItem?.color,\r\n            }}\r\n          >\r\n            A\r\n          </span>\r\n          <ChevronDown className=\"h-4 w-4\" />\r\n        </Button>\r\n      </PopoverTrigger>\r\n\r\n      <PopoverContent\r\n        sideOffset={5}\r\n        className=\"my-1 flex max-h-80 w-48 flex-col overflow-hidden overflow-y-auto rounded border p-1 shadow-xl\"\r\n        align=\"start\"\r\n      >\r\n        <div className=\"flex flex-col\">\r\n          <div className=\"text-muted-foreground my-1 px-2 text-sm font-semibold\">\r\n            Color\r\n          </div>\r\n          {TEXT_COLORS.map(({ name, color }) => (\r\n            <EditorBubbleItem\r\n              key={name}\r\n              onSelect={() => {\r\n                editor.commands.unsetColor();\r\n                name !== \"Default\" &&\r\n                  editor\r\n                    .chain()\r\n                    .focus()\r\n                    .setColor(color || \"\")\r\n                    .run();\r\n                onOpenChange(false);\r\n              }}\r\n              className=\"hover:bg-accent flex cursor-pointer items-center justify-between px-2 py-1 text-sm\"\r\n            >\r\n              <div className=\"flex items-center gap-2\">\r\n                <div\r\n                  className=\"rounded-sm border px-2 py-px font-medium\"\r\n                  style={{ color }}\r\n                >\r\n                  A\r\n                </div>\r\n                <span>{name}</span>\r\n              </div>\r\n            </EditorBubbleItem>\r\n          ))}\r\n        </div>\r\n        <div>\r\n          <div className=\"text-muted-foreground my-1 px-2 text-sm font-semibold\">\r\n            Background\r\n          </div>\r\n          {HIGHLIGHT_COLORS.map(({ name, color }) => (\r\n            <EditorBubbleItem\r\n              key={name}\r\n              onSelect={() => {\r\n                editor.commands.unsetHighlight();\r\n                name !== \"Default\" &&\r\n                  editor.chain().focus().setHighlight({ color }).run();\r\n                onOpenChange(false);\r\n              }}\r\n              className=\"hover:bg-accent flex cursor-pointer items-center justify-between px-2 py-1 text-sm\"\r\n            >\r\n              <div className=\"flex items-center gap-2\">\r\n                <div\r\n                  className=\"rounded-sm border px-2 py-px font-medium\"\r\n                  style={{ backgroundColor: color }}\r\n                >\r\n                  A\r\n                </div>\r\n                <span>{name}</span>\r\n              </div>\r\n              {editor.isActive(\"highlight\", { color }) && (\r\n                <Check className=\"h-4 w-4\" />\r\n              )}\r\n            </EditorBubbleItem>\r\n          ))}\r\n        </div>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AACA;AAAA;AAEA;AACA;;;;;;;AAMA,MAAM,cAAqC;IACzC;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;CACD;AAED,MAAM,mBAA0C;IAC9C;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;CACD;AAOM,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,YAAY,EAAsB;;IACtE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAE3B,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,kBAAkB,YAAY,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GACjD,OAAO,QAAQ,CAAC,aAAa;YAAE;QAAM;IAGvC,MAAM,sBAAsB,iBAAiB,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAC1D,OAAO,QAAQ,CAAC,aAAa;YAAE;QAAM;IAGvC,qBACE,sSAAC,sIAAA,CAAA,UAAO;QAAC,OAAO;QAAM,MAAM;QAAM,cAAc;;0BAC9C,sSAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,sSAAC,qIAAA,CAAA,SAAM;oBAAC,MAAK;oBAAK,WAAU;oBAAqB,SAAQ;;sCACvD,sSAAC;4BACC,WAAU;4BACV,OAAO;gCACL,OAAO,iBAAiB;gCACxB,iBAAiB,qBAAqB;4BACxC;sCACD;;;;;;sCAGD,sSAAC,2SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAI3B,sSAAC,sIAAA,CAAA,iBAAc;gBACb,YAAY;gBACZ,WAAU;gBACV,OAAM;;kCAEN,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;0CAAwD;;;;;;4BAGtE,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,iBAC/B,sSAAC,kQAAA,CAAA,mBAAgB;oCAEf,UAAU;wCACR,OAAO,QAAQ,CAAC,UAAU;wCAC1B,SAAS,aACP,OACG,KAAK,GACL,KAAK,GACL,QAAQ,CAAC,SAAS,IAClB,GAAG;wCACR,aAAa;oCACf;oCACA,WAAU;8CAEV,cAAA,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDACC,WAAU;gDACV,OAAO;oDAAE;gDAAM;0DAChB;;;;;;0DAGD,sSAAC;0DAAM;;;;;;;;;;;;mCApBJ;;;;;;;;;;;kCAyBX,sSAAC;;0CACC,sSAAC;gCAAI,WAAU;0CAAwD;;;;;;4BAGtE,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,iBACpC,sSAAC,kQAAA,CAAA,mBAAgB;oCAEf,UAAU;wCACR,OAAO,QAAQ,CAAC,cAAc;wCAC9B,SAAS,aACP,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,CAAC;4CAAE;wCAAM,GAAG,GAAG;wCACpD,aAAa;oCACf;oCACA,WAAU;;sDAEV,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB;oDAAM;8DACjC;;;;;;8DAGD,sSAAC;8DAAM;;;;;;;;;;;;wCAER,OAAO,QAAQ,CAAC,aAAa;4CAAE;wCAAM,oBACpC,sSAAC,2RAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;mCAnBd;;;;;;;;;;;;;;;;;;;;;;;AA2BnB;GAlGa;;QACQ,gUAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 6607, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/editor/selectors/link-selector.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Button } from \"../../ui/button\";\r\nimport { PopoverContent } from \"../../ui/popover\";\r\nimport { cn } from \"../../../lib/utils\";\r\nimport { Popover, PopoverTrigger } from \"@radix-ui/react-popover\";\r\nimport { Check, Trash } from \"lucide-react\";\r\nimport { useEditor } from \"novel\";\r\nimport { useEffect, useRef } from \"react\";\r\n\r\nexport function isValidUrl(url: string) {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch (_e) {\r\n    return false;\r\n  }\r\n}\r\nexport function getUrlFromString(str: string) {\r\n  if (isValidUrl(str)) return str;\r\n  try {\r\n    if (str.includes(\".\") && !str.includes(\" \")) {\r\n      return new URL(`https://${str}`).toString();\r\n    }\r\n  } catch (_e) {\r\n    return null;\r\n  }\r\n}\r\ninterface LinkSelectorProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n}\r\n\r\nexport const LinkSelector = ({ open, onOpenChange }: LinkSelectorProps) => {\r\n  const inputRef = useRef<HTMLInputElement>(null);\r\n  const { editor } = useEditor();\r\n\r\n  // Autofocus on input by default\r\n  useEffect(() => {\r\n    inputRef.current?.focus();\r\n  });\r\n  if (!editor) return null;\r\n\r\n  return (\r\n    <Popover modal={true} open={open} onOpenChange={onOpenChange}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          size=\"sm\"\r\n          variant=\"ghost\"\r\n          className=\"gap-2 rounded-none border-none\"\r\n        >\r\n          <p className=\"text-base\">↗</p>\r\n          <p\r\n            className={cn(\"underline decoration-stone-400 underline-offset-4\", {\r\n              \"text-blue-500\": editor.isActive(\"link\"),\r\n            })}\r\n          >\r\n            Link\r\n          </p>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent align=\"start\" className=\"w-60 p-0\" sideOffset={10}>\r\n        <form\r\n          onSubmit={(e) => {\r\n            const target = e.currentTarget as HTMLFormElement;\r\n            e.preventDefault();\r\n            const input = target[0] as HTMLInputElement;\r\n            const url = getUrlFromString(input.value);\r\n            if (url) {\r\n              editor.chain().focus().setLink({ href: url }).run();\r\n              onOpenChange(false);\r\n            }\r\n          }}\r\n          className=\"flex p-1\"\r\n        >\r\n          <input\r\n            ref={inputRef}\r\n            type=\"text\"\r\n            placeholder=\"Paste a link\"\r\n            className=\"bg-background flex-1 p-1 text-sm outline-none\"\r\n            defaultValue={editor.getAttributes(\"link\").href || \"\"}\r\n          />\r\n          {editor.getAttributes(\"link\").href ? (\r\n            <Button\r\n              size=\"icon\"\r\n              variant=\"outline\"\r\n              type=\"button\"\r\n              className=\"flex h-8 items-center rounded-sm p-1 text-red-600 transition-all hover:bg-red-100 dark:hover:bg-red-800\"\r\n              onClick={() => {\r\n                editor.chain().focus().unsetLink().run();\r\n                if (inputRef.current) inputRef.current.value = \"\";\r\n                onOpenChange(false);\r\n              }}\r\n            >\r\n              <Trash className=\"h-4 w-4\" />\r\n            </Button>\r\n          ) : (\r\n            <Button size=\"icon\" className=\"h-8\">\r\n              <Check className=\"h-4 w-4\" />\r\n            </Button>\r\n          )}\r\n        </form>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;AAE/B;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;;;AAEO,SAAS,WAAW,GAAW;IACpC,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAO,IAAI;QACX,OAAO;IACT;AACF;AACO,SAAS,iBAAiB,GAAW;IAC1C,IAAI,WAAW,MAAM,OAAO;IAC5B,IAAI;QACF,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM;YAC3C,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ;QAC3C;IACF,EAAE,OAAO,IAAI;QACX,OAAO;IACT;AACF;AAMO,MAAM,eAAe,CAAC,EAAE,IAAI,EAAE,YAAY,EAAqB;;IACpE,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAE3B,gCAAgC;IAChC,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;kCAAE;YACR,SAAS,OAAO,EAAE;QACpB;;IACA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,sSAAC,gRAAA,CAAA,UAAO;QAAC,OAAO;QAAM,MAAM;QAAM,cAAc;;0BAC9C,sSAAC,gRAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,sSAAC,qIAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAQ;oBACR,WAAU;;sCAEV,sSAAC;4BAAE,WAAU;sCAAY;;;;;;sCACzB,sSAAC;4BACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;gCACjE,iBAAiB,OAAO,QAAQ,CAAC;4BACnC;sCACD;;;;;;;;;;;;;;;;;0BAKL,sSAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAM;gBAAQ,WAAU;gBAAW,YAAY;0BAC7D,cAAA,sSAAC;oBACC,UAAU,CAAC;wBACT,MAAM,SAAS,EAAE,aAAa;wBAC9B,EAAE,cAAc;wBAChB,MAAM,QAAQ,MAAM,CAAC,EAAE;wBACvB,MAAM,MAAM,iBAAiB,MAAM,KAAK;wBACxC,IAAI,KAAK;4BACP,OAAO,KAAK,GAAG,KAAK,GAAG,OAAO,CAAC;gCAAE,MAAM;4BAAI,GAAG,GAAG;4BACjD,aAAa;wBACf;oBACF;oBACA,WAAU;;sCAEV,sSAAC;4BACC,KAAK;4BACL,MAAK;4BACL,aAAY;4BACZ,WAAU;4BACV,cAAc,OAAO,aAAa,CAAC,QAAQ,IAAI,IAAI;;;;;;wBAEpD,OAAO,aAAa,CAAC,QAAQ,IAAI,iBAChC,sSAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;gCACP,OAAO,KAAK,GAAG,KAAK,GAAG,SAAS,GAAG,GAAG;gCACtC,IAAI,SAAS,OAAO,EAAE,SAAS,OAAO,CAAC,KAAK,GAAG;gCAC/C,aAAa;4BACf;sCAEA,cAAA,sSAAC,2RAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;iDAGnB,sSAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAO,WAAU;sCAC5B,cAAA,sSAAC,2RAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B;GAxEa;;QAEQ,gUAAA,CAAA,YAAS;;;KAFjB", "debugId": null}}, {"offset": {"line": 6803, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/editor/selectors/math-selector.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Button } from \"../../ui/button\";\r\nimport { cn } from \"../../../lib/utils\";\r\nimport { SigmaIcon } from \"lucide-react\";\r\nimport { useEditor } from \"novel\";\r\n\r\nexport const MathSelector = () => {\r\n  const { editor } = useEditor();\r\n\r\n  if (!editor) return null;\r\n\r\n  return (\r\n    <Button\r\n      variant=\"ghost\"\r\n      size=\"sm\"\r\n      className=\"w-12 rounded-none\"\r\n      onClick={(evt) => {\r\n        if (editor.isActive(\"math\")) {\r\n          editor.chain().focus().unsetLatex().run();\r\n        } else {\r\n          const { from, to } = editor.state.selection;\r\n          const latex = editor.state.doc.textBetween(from, to);\r\n\r\n          if (!latex) return;\r\n\r\n          editor.chain().focus().setLatex({ latex }).run();\r\n        }\r\n      }}\r\n    >\r\n      <SigmaIcon\r\n        className={cn(\"size-4\", { \"text-blue-500\": editor.isActive(\"math\") })}\r\n        strokeWidth={2.3}\r\n      />\r\n    </Button>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AACA;AACA;;;;;;;AAEO,MAAM,eAAe;;IAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAE3B,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,sSAAC,qIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,WAAU;QACV,SAAS,CAAC;YACR,IAAI,OAAO,QAAQ,CAAC,SAAS;gBAC3B,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;YACzC,OAAO;gBACL,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,OAAO,KAAK,CAAC,SAAS;gBAC3C,MAAM,QAAQ,OAAO,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM;gBAEjD,IAAI,CAAC,OAAO;gBAEZ,OAAO,KAAK,GAAG,KAAK,GAAG,QAAQ,CAAC;oBAAE;gBAAM,GAAG,GAAG;YAChD;QACF;kBAEA,cAAA,sSAAC,+RAAA,CAAA,YAAS;YACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;gBAAE,iBAAiB,OAAO,QAAQ,CAAC;YAAQ;YACnE,aAAa;;;;;;;;;;;AAIrB;GA7Ba;;QACQ,gUAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 6872, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/editor/selectors/node-selector.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport {\r\n  Check,\r\n  CheckSquare,\r\n  ChevronDown,\r\n  Code,\r\n  Heading1,\r\n  Heading2,\r\n  Heading3,\r\n  ListOrdered,\r\n  type LucideIcon,\r\n  TextIcon,\r\n  TextQuote,\r\n} from \"lucide-react\";\r\nimport { EditorBubbleItem, useEditor } from \"novel\";\r\n\r\nimport { Button } from \"../../ui/button\";\r\nimport { PopoverContent, PopoverTrigger } from \"../../ui/popover\";\r\nimport { Popover } from \"@radix-ui/react-popover\";\r\n\r\nexport type SelectorItem = {\r\n  name: string;\r\n  icon: LucideIcon;\r\n  command: (\r\n    editor: NonNullable<ReturnType<typeof useEditor>[\"editor\"]>,\r\n  ) => void;\r\n  isActive: (\r\n    editor: NonNullable<ReturnType<typeof useEditor>[\"editor\"]>,\r\n  ) => boolean;\r\n};\r\n\r\nconst items: SelectorItem[] = [\r\n  {\r\n    name: \"Text\",\r\n    icon: TextIcon,\r\n    command: (editor) => editor.chain().focus().clearNodes().run(),\r\n    // I feel like there has to be a more efficient way to do this – feel free to PR if you know how!\r\n    isActive: (editor) =>\r\n      editor.isActive(\"paragraph\") &&\r\n      !editor.isActive(\"bulletList\") &&\r\n      !editor.isActive(\"orderedList\"),\r\n  },\r\n  {\r\n    name: \"Heading 1\",\r\n    icon: Heading1,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleHeading({ level: 1 }).run(),\r\n    isActive: (editor) => editor.isActive(\"heading\", { level: 1 }),\r\n  },\r\n  {\r\n    name: \"Heading 2\",\r\n    icon: Heading2,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleHeading({ level: 2 }).run(),\r\n    isActive: (editor) => editor.isActive(\"heading\", { level: 2 }),\r\n  },\r\n  {\r\n    name: \"Heading 3\",\r\n    icon: Heading3,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleHeading({ level: 3 }).run(),\r\n    isActive: (editor) => editor.isActive(\"heading\", { level: 3 }),\r\n  },\r\n  {\r\n    name: \"To-do List\",\r\n    icon: CheckSquare,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleTaskList().run(),\r\n    isActive: (editor) => editor.isActive(\"taskItem\"),\r\n  },\r\n  {\r\n    name: \"Bullet List\",\r\n    icon: ListOrdered,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleBulletList().run(),\r\n    isActive: (editor) => editor.isActive(\"bulletList\"),\r\n  },\r\n  {\r\n    name: \"Numbered List\",\r\n    icon: ListOrdered,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleOrderedList().run(),\r\n    isActive: (editor) => editor.isActive(\"orderedList\"),\r\n  },\r\n  {\r\n    name: \"Quote\",\r\n    icon: TextQuote,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleBlockquote().run(),\r\n    isActive: (editor) => editor.isActive(\"blockquote\"),\r\n  },\r\n  {\r\n    name: \"Code\",\r\n    icon: Code,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleCodeBlock().run(),\r\n    isActive: (editor) => editor.isActive(\"codeBlock\"),\r\n  },\r\n];\r\ninterface NodeSelectorProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n}\r\n\r\nexport const NodeSelector = ({ open, onOpenChange }: NodeSelectorProps) => {\r\n  const { editor } = useEditor();\r\n  if (!editor) return null;\r\n  const activeItem = items.filter((item) => item.isActive(editor)).pop() ?? {\r\n    name: \"Multiple\",\r\n  };\r\n\r\n  return (\r\n    <Popover modal={true} open={open} onOpenChange={onOpenChange}>\r\n      <PopoverTrigger\r\n        asChild\r\n        className=\"hover:bg-accent gap-2 rounded-none border-none focus:ring-0\"\r\n      >\r\n        <Button size=\"sm\" variant=\"ghost\" className=\"gap-2\">\r\n          <span className=\"text-sm whitespace-nowrap\">{activeItem.name}</span>\r\n          <ChevronDown className=\"h-4 w-4\" />\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent sideOffset={5} align=\"start\" className=\"w-48 p-1\">\r\n        {items.map((item) => (\r\n          <EditorBubbleItem\r\n            key={item.name}\r\n            onSelect={(editor) => {\r\n              item.command(editor);\r\n              onOpenChange(false);\r\n            }}\r\n            className=\"hover:bg-accent flex cursor-pointer items-center justify-between rounded-sm px-2 py-1 text-sm\"\r\n          >\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"rounded-sm border p-1\">\r\n                <item.icon className=\"h-3 w-3\" />\r\n              </div>\r\n              <span>{item.name}</span>\r\n            </div>\r\n            {activeItem.name === item.name && <Check className=\"h-4 w-4\" />}\r\n          </EditorBubbleItem>\r\n        ))}\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAAA;AAEA;AACA;AACA;;;;;;;;AAaA,MAAM,QAAwB;IAC5B;QACE,MAAM;QACN,MAAM,6RAAA,CAAA,WAAQ;QACd,SAAS,CAAC,SAAW,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;QAC5D,iGAAiG;QACjG,UAAU,CAAC,SACT,OAAO,QAAQ,CAAC,gBAChB,CAAC,OAAO,QAAQ,CAAC,iBACjB,CAAC,OAAO,QAAQ,CAAC;IACrB;IACA;QACE,MAAM;QACN,MAAM,qSAAA,CAAA,WAAQ;QACd,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,aAAa,CAAC;gBAAE,OAAO;YAAE,GAAG,GAAG;QACrE,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC,WAAW;gBAAE,OAAO;YAAE;IAC9D;IACA;QACE,MAAM;QACN,MAAM,qSAAA,CAAA,WAAQ;QACd,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,aAAa,CAAC;gBAAE,OAAO;YAAE,GAAG,GAAG;QACrE,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC,WAAW;gBAAE,OAAO;YAAE;IAC9D;IACA;QACE,MAAM;QACN,MAAM,qSAAA,CAAA,WAAQ;QACd,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,aAAa,CAAC;gBAAE,OAAO;YAAE,GAAG,GAAG;QACrE,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC,WAAW;gBAAE,OAAO;YAAE;IAC9D;IACA;QACE,MAAM;QACN,MAAM,kTAAA,CAAA,cAAW;QACjB,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,cAAc,GAAG,GAAG;QAC1D,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;IACxC;IACA;QACE,MAAM;QACN,MAAM,2SAAA,CAAA,cAAW;QACjB,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,gBAAgB,GAAG,GAAG;QAC5D,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;IACxC;IACA;QACE,MAAM;QACN,MAAM,2SAAA,CAAA,cAAW;QACjB,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,iBAAiB,GAAG,GAAG;QAC7D,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;IACxC;IACA;QACE,MAAM;QACN,MAAM,uSAAA,CAAA,YAAS;QACf,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,gBAAgB,GAAG,GAAG;QAC5D,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;IACxC;IACA;QACE,MAAM;QACN,MAAM,yRAAA,CAAA,OAAI;QACV,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,eAAe,GAAG,GAAG;QAC3D,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;IACxC;CACD;AAMM,MAAM,eAAe,CAAC,EAAE,IAAI,EAAE,YAAY,EAAqB;;IACpE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAC3B,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,CAAC,SAAS,GAAG,MAAM;QACxE,MAAM;IACR;IAEA,qBACE,sSAAC,gRAAA,CAAA,UAAO;QAAC,OAAO;QAAM,MAAM;QAAM,cAAc;;0BAC9C,sSAAC,sIAAA,CAAA,iBAAc;gBACb,OAAO;gBACP,WAAU;0BAEV,cAAA,sSAAC,qIAAA,CAAA,SAAM;oBAAC,MAAK;oBAAK,SAAQ;oBAAQ,WAAU;;sCAC1C,sSAAC;4BAAK,WAAU;sCAA6B,WAAW,IAAI;;;;;;sCAC5D,sSAAC,2SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAG3B,sSAAC,sIAAA,CAAA,iBAAc;gBAAC,YAAY;gBAAG,OAAM;gBAAQ,WAAU;0BACpD,MAAM,GAAG,CAAC,CAAC,qBACV,sSAAC,kQAAA,CAAA,mBAAgB;wBAEf,UAAU,CAAC;4BACT,KAAK,OAAO,CAAC;4BACb,aAAa;wBACf;wBACA,WAAU;;0CAEV,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;kDACb,cAAA,sSAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAEvB,sSAAC;kDAAM,KAAK,IAAI;;;;;;;;;;;;4BAEjB,WAAW,IAAI,KAAK,KAAK,IAAI,kBAAI,sSAAC,2RAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;uBAb9C,KAAK,IAAI;;;;;;;;;;;;;;;;AAmB1B;GAxCa;;QACQ,gUAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 7098, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/ui/icons/magic.tsx"], "sourcesContent": ["export default function Magic({ className }: { className: string }) {\r\n  return (\r\n    <svg\r\n      width=\"469\"\r\n      height=\"469\"\r\n      viewBox=\"0 0 469 469\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      shapeRendering=\"geometricPrecision\"\r\n      stroke=\"currentColor\"\r\n      strokeLinecap=\"round\"\r\n      strokeLinejoin=\"round\"\r\n      strokeWidth=\"1.5\"\r\n      className={className}\r\n    >\r\n      <title>Magic AI icon</title>\r\n\r\n      <path\r\n        d=\"M237.092 62.3004L266.754 71.4198C267.156 71.5285 267.51 71.765 267.765 72.0934C268.02 72.4218 268.161 72.8243 268.166 73.2399C268.172 73.6555 268.042 74.0616 267.796 74.3967C267.55 74.7318 267.201 74.9777 266.803 75.097L237.141 84.3145C236.84 84.4058 236.566 84.5699 236.344 84.7922C236.121 85.0146 235.957 85.2883 235.866 85.5893L226.747 115.252C226.638 115.653 226.401 116.008 226.073 116.263C225.745 116.517 225.342 116.658 224.926 116.664C224.511 116.669 224.105 116.539 223.77 116.293C223.435 116.047 223.189 115.699 223.069 115.301L213.852 85.6383C213.761 85.3374 213.597 85.0636 213.374 84.8412C213.152 84.6189 212.878 84.4548 212.577 84.3635L182.914 75.2441C182.513 75.1354 182.158 74.8989 181.904 74.5705C181.649 74.2421 181.508 73.8396 181.503 73.424C181.497 73.0084 181.627 72.6023 181.873 72.2672C182.119 71.9321 182.467 71.6863 182.865 71.5669L212.528 62.3494C212.829 62.2582 213.103 62.0941 213.325 61.8717C213.547 61.6494 213.712 61.3756 213.803 61.0747L222.922 31.4121C223.031 31.0109 223.267 30.656 223.596 30.4013C223.924 30.1465 224.327 30.0057 224.742 30.0002C225.158 29.9946 225.564 30.1247 225.899 30.3706C226.234 30.6165 226.48 30.9649 226.599 31.363L235.817 61.0257C235.908 61.3266 236.072 61.6003 236.295 61.8227C236.517 62.0451 236.791 62.2091 237.092 62.3004Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n      <path\r\n        d=\"M155.948 155.848L202.771 168.939C203.449 169.131 204.045 169.539 204.47 170.101C204.895 170.663 205.125 171.348 205.125 172.052C205.125 172.757 204.895 173.442 204.47 174.004C204.045 174.566 203.449 174.974 202.771 175.166L155.899 188.06C155.361 188.209 154.87 188.496 154.475 188.891C154.079 189.286 153.793 189.777 153.644 190.316L140.553 237.138C140.361 237.816 139.953 238.413 139.391 238.838C138.829 239.262 138.144 239.492 137.44 239.492C136.735 239.492 136.05 239.262 135.488 238.838C134.927 238.413 134.519 237.816 134.327 237.138L121.432 190.267C121.283 189.728 120.997 189.237 120.601 188.842C120.206 188.446 119.715 188.16 119.177 188.011L72.3537 174.92C71.676 174.728 71.0795 174.32 70.6547 173.759C70.2299 173.197 70 172.512 70 171.807C70 171.103 70.2299 170.418 70.6547 169.856C71.0795 169.294 71.676 168.886 72.3537 168.694L119.226 155.799C119.764 155.65 120.255 155.364 120.65 154.969C121.046 154.573 121.332 154.082 121.481 153.544L134.572 106.721C134.764 106.043 135.172 105.447 135.734 105.022C136.295 104.597 136.981 104.367 137.685 104.367C138.389 104.367 139.075 104.597 139.637 105.022C140.198 105.447 140.606 106.043 140.798 106.721L153.693 153.593C153.842 154.131 154.128 154.622 154.524 155.018C154.919 155.413 155.41 155.699 155.948 155.848Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n      <path\r\n        d=\"M386.827 289.992C404.33 292.149 403.84 305.828 386.876 307.299C346.623 310.829 298.869 316.271 282.199 360.005C274.844 379.192 269.942 403.2 267.49 432.029C267.427 432.846 267.211 433.626 266.856 434.319C266.501 435.012 266.015 435.602 265.431 436.05C254.988 444.041 251.212 434.186 250.183 425.606C239.2 332.353 214.588 316.909 124.668 306.122C123.892 306.031 123.151 305.767 122.504 305.35C121.857 304.933 121.322 304.375 120.942 303.72C116.399 295.679 119.324 291.038 129.718 289.796C224.688 278.47 236.062 262.83 250.183 169.331C252.177 156.355 257.259 154.083 265.431 162.516C266.51 163.593 267.202 165.099 267.392 166.782C279.257 258.564 293.328 278.617 386.827 289.992Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS,MAAM,EAAE,SAAS,EAAyB;IAChE,qBACE,sSAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;QACN,gBAAe;QACf,QAAO;QACP,eAAc;QACd,gBAAe;QACf,aAAY;QACZ,WAAW;;0BAEX,sSAAC;0BAAM;;;;;;0BAEP,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;KA/BwB", "debugId": null}}, {"offset": {"line": 7167, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/ui/command.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Command as CommandPrimitive } from \"cmdk\"\r\nimport { SearchIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"~/components/ui/dialog\"\r\n\r\nfunction Command({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive>) {\r\n  return (\r\n    <CommandPrimitive\r\n      data-slot=\"command\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandDialog({\r\n  title = \"Command Palette\",\r\n  description = \"Search for a command to run...\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof Dialog> & {\r\n  title?: string\r\n  description?: string\r\n}) {\r\n  return (\r\n    <Dialog {...props}>\r\n      <DialogHeader className=\"sr-only\">\r\n        <DialogTitle>{title}</DialogTitle>\r\n        <DialogDescription>{description}</DialogDescription>\r\n      </DialogHeader>\r\n      <DialogContent className=\"overflow-hidden p-0\">\r\n        <Command className=\"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\r\n          {children}\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nfunction CommandInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Input>) {\r\n  return (\r\n    <div\r\n      data-slot=\"command-input-wrapper\"\r\n      className=\"flex h-9 items-center gap-2 border-b px-3\"\r\n    >\r\n      <SearchIcon className=\"size-4 shrink-0 opacity-50\" />\r\n      <CommandPrimitive.Input\r\n        data-slot=\"command-input\"\r\n        className={cn(\r\n          \"placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction CommandList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.List>) {\r\n  return (\r\n    <CommandPrimitive.List\r\n      data-slot=\"command-list\"\r\n      className={cn(\r\n        \"max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandEmpty({\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Empty>) {\r\n  return (\r\n    <CommandPrimitive.Empty\r\n      data-slot=\"command-empty\"\r\n      className=\"py-6 text-center text-sm\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandGroup({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Group>) {\r\n  return (\r\n    <CommandPrimitive.Group\r\n      data-slot=\"command-group\"\r\n      className={cn(\r\n        \"text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Separator>) {\r\n  return (\r\n    <CommandPrimitive.Separator\r\n      data-slot=\"command-separator\"\r\n      className={cn(\"bg-border -mx-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Item>) {\r\n  return (\r\n    <CommandPrimitive.Item\r\n      data-slot=\"command-item\"\r\n      className={cn(\r\n        \"data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"command-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Command,\r\n  CommandDialog,\r\n  CommandInput,\r\n  CommandList,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandShortcut,\r\n  CommandSeparator,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA;AACA;AAEA;AACA;AAPA;;;;;;AAeA,SAAS,QAAQ,EACf,SAAS,EACT,GAAG,OAC2C;IAC9C,qBACE,sSAAC,qPAAA,CAAA,UAAgB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,cAAc,EACrB,QAAQ,iBAAiB,EACzB,cAAc,gCAAgC,EAC9C,QAAQ,EACR,GAAG,OAIJ;IACC,qBACE,sSAAC,qIAAA,CAAA,SAAM;QAAE,GAAG,KAAK;;0BACf,sSAAC,qIAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,sSAAC,qIAAA,CAAA,cAAW;kCAAE;;;;;;kCACd,sSAAC,qIAAA,CAAA,oBAAiB;kCAAE;;;;;;;;;;;;0BAEtB,sSAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,sSAAC;oBAAQ,WAAU;8BAChB;;;;;;;;;;;;;;;;;AAKX;MAtBS;AAwBT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,sSAAC;QACC,aAAU;QACV,WAAU;;0BAEV,sSAAC,iSAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BACtB,sSAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4JACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0NACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,SAAS;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;QACrC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 7372, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/editor/generative/ai-completion-command.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { CommandGroup, CommandItem, CommandSeparator } from \"../../ui/command\";\r\nimport { useEditor } from \"novel\";\r\nimport { Check, TextQuote, TrashIcon } from \"lucide-react\";\r\n\r\nconst AICompletionCommands = ({\r\n  completion,\r\n  onDiscard,\r\n}: {\r\n  completion: string;\r\n  onDiscard: () => void;\r\n}) => {\r\n  const { editor } = useEditor();\r\n  if (!editor) return null;\r\n  return (\r\n    <>\r\n      <CommandGroup>\r\n        <CommandItem\r\n          className=\"gap-2 px-4\"\r\n          value=\"replace\"\r\n          onSelect={() => {\r\n            const selection = editor.view.state.selection;\r\n            editor\r\n              .chain()\r\n              .focus()\r\n              .insertContentAt(\r\n                {\r\n                  from: selection.from,\r\n                  to: selection.to,\r\n                },\r\n                completion,\r\n              )\r\n              .run();\r\n          }}\r\n        >\r\n          <Check className=\"text-muted-foreground h-4 w-4\" />\r\n          Replace selection\r\n        </CommandItem>\r\n        <CommandItem\r\n          className=\"gap-2 px-4\"\r\n          value=\"insert\"\r\n          onSelect={() => {\r\n            const selection = editor.view.state.selection;\r\n            editor\r\n              .chain()\r\n              .focus()\r\n              .insertContentAt(selection.to + 1, completion)\r\n              .run();\r\n          }}\r\n        >\r\n          <TextQuote className=\"text-muted-foreground h-4 w-4\" />\r\n          Insert below\r\n        </CommandItem>\r\n      </CommandGroup>\r\n      <CommandSeparator />\r\n\r\n      <CommandGroup>\r\n        <CommandItem onSelect={onDiscard} value=\"thrash\" className=\"gap-2 px-4\">\r\n          <TrashIcon className=\"text-muted-foreground h-4 w-4\" />\r\n          Discard\r\n        </CommandItem>\r\n      </CommandGroup>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AICompletionCommands;\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AACA;AAAA;AAAA;;;;;;AAEA,MAAM,uBAAuB,CAAC,EAC5B,UAAU,EACV,SAAS,EAIV;;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAC3B,IAAI,CAAC,QAAQ,OAAO;IACpB,qBACE;;0BACE,sSAAC,sIAAA,CAAA,eAAY;;kCACX,sSAAC,sIAAA,CAAA,cAAW;wBACV,WAAU;wBACV,OAAM;wBACN,UAAU;4BACR,MAAM,YAAY,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS;4BAC7C,OACG,KAAK,GACL,KAAK,GACL,eAAe,CACd;gCACE,MAAM,UAAU,IAAI;gCACpB,IAAI,UAAU,EAAE;4BAClB,GACA,YAED,GAAG;wBACR;;0CAEA,sSAAC,2RAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAkC;;;;;;;kCAGrD,sSAAC,sIAAA,CAAA,cAAW;wBACV,WAAU;wBACV,OAAM;wBACN,UAAU;4BACR,MAAM,YAAY,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS;4BAC7C,OACG,KAAK,GACL,KAAK,GACL,eAAe,CAAC,UAAU,EAAE,GAAG,GAAG,YAClC,GAAG;wBACR;;0CAEA,sSAAC,uSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAkC;;;;;;;;;;;;;0BAI3D,sSAAC,sIAAA,CAAA,mBAAgB;;;;;0BAEjB,sSAAC,sIAAA,CAAA,eAAY;0BACX,cAAA,sSAAC,sIAAA,CAAA,cAAW;oBAAC,UAAU;oBAAW,OAAM;oBAAS,WAAU;;sCACzD,sSAAC,+RAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAkC;;;;;;;;;;;;;;AAMjE;GA3DM;;QAOe,gUAAA,CAAA,YAAS;;;KAPxB;uCA6DS", "debugId": null}}, {"offset": {"line": 7500, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/editor/generative/ai-selector-commands.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport {\r\n  ArrowDownWideNarrow,\r\n  CheckCheck,\r\n  RefreshCcwDot,\r\n  StepForward,\r\n  WrapText,\r\n} from \"lucide-react\";\r\nimport { getPrevText, useEditor } from \"novel\";\r\nimport { CommandGroup, CommandItem, CommandSeparator } from \"../../ui/command\";\r\n\r\nconst options = [\r\n  {\r\n    value: \"improve\",\r\n    label: \"Improve writing\",\r\n    icon: RefreshCcwDot,\r\n  },\r\n  // TODO: add this back in\r\n  // {\r\n  //   value: \"fix\",\r\n  //   label: \"Fix grammar\",\r\n  //   icon: CheckCheck,\r\n  // },\r\n  {\r\n    value: \"shorter\",\r\n    label: \"Make shorter\",\r\n    icon: ArrowDownWideNarrow,\r\n  },\r\n  {\r\n    value: \"longer\",\r\n    label: \"Make longer\",\r\n    icon: WrapText,\r\n  },\r\n];\r\n\r\ninterface AISelectorCommandsProps {\r\n  onSelect: (value: string, option: string) => void;\r\n}\r\n\r\nconst AISelectorCommands = ({ onSelect }: AISelectorCommandsProps) => {\r\n  const { editor } = useEditor();\r\n  if (!editor) return null;\r\n  return (\r\n    <>\r\n      <CommandGroup heading=\"Edit or review selection\">\r\n        {options.map((option) => (\r\n          <CommandItem\r\n            onSelect={(value) => {\r\n              const slice = editor.state.selection.content();\r\n              const text = editor.storage.markdown.serializer.serialize(\r\n                slice.content,\r\n              );\r\n              onSelect(text, value);\r\n            }}\r\n            className=\"flex gap-2 px-4\"\r\n            key={option.value}\r\n            value={option.value}\r\n          >\r\n            <option.icon className=\"h-4 w-4 text-purple-500\" />\r\n            {option.label}\r\n          </CommandItem>\r\n        ))}\r\n      </CommandGroup>\r\n      <CommandSeparator />\r\n      <CommandGroup heading=\"Use AI to do more\">\r\n        <CommandItem\r\n          onSelect={() => {\r\n            const pos = editor.state.selection.from;\r\n            const text = getPrevText(editor, pos);\r\n            onSelect(text, \"continue\");\r\n          }}\r\n          value=\"continue\"\r\n          className=\"gap-2 px-4\"\r\n        >\r\n          <StepForward className=\"h-4 w-4 text-purple-500\" />\r\n          Continue writing\r\n        </CommandItem>\r\n      </CommandGroup>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AISelectorCommands;\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AAAA;AAAA;AAOA;AAAA;AACA;;;;;;AAEA,MAAM,UAAU;IACd;QACE,OAAO;QACP,OAAO;QACP,MAAM,mTAAA,CAAA,gBAAa;IACrB;IACA,yBAAyB;IACzB,IAAI;IACJ,kBAAkB;IAClB,0BAA0B;IAC1B,sBAAsB;IACtB,KAAK;IACL;QACE,OAAO;QACP,OAAO;QACP,MAAM,mUAAA,CAAA,sBAAmB;IAC3B;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,qSAAA,CAAA,WAAQ;IAChB;CACD;AAMD,MAAM,qBAAqB,CAAC,EAAE,QAAQ,EAA2B;;IAC/D,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAC3B,IAAI,CAAC,QAAQ,OAAO;IACpB,qBACE;;0BACE,sSAAC,sIAAA,CAAA,eAAY;gBAAC,SAAQ;0BACnB,QAAQ,GAAG,CAAC,CAAC,uBACZ,sSAAC,sIAAA,CAAA,cAAW;wBACV,UAAU,CAAC;4BACT,MAAM,QAAQ,OAAO,KAAK,CAAC,SAAS,CAAC,OAAO;4BAC5C,MAAM,OAAO,OAAO,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CACvD,MAAM,OAAO;4BAEf,SAAS,MAAM;wBACjB;wBACA,WAAU;wBAEV,OAAO,OAAO,KAAK;;0CAEnB,sSAAC,OAAO,IAAI;gCAAC,WAAU;;;;;;4BACtB,OAAO,KAAK;;uBAJR,OAAO,KAAK;;;;;;;;;;0BAQvB,sSAAC,sIAAA,CAAA,mBAAgB;;;;;0BACjB,sSAAC,sIAAA,CAAA,eAAY;gBAAC,SAAQ;0BACpB,cAAA,sSAAC,sIAAA,CAAA,cAAW;oBACV,UAAU;wBACR,MAAM,MAAM,OAAO,KAAK,CAAC,SAAS,CAAC,IAAI;wBACvC,MAAM,OAAO,CAAA,GAAA,kQAAA,CAAA,cAAW,AAAD,EAAE,QAAQ;wBACjC,SAAS,MAAM;oBACjB;oBACA,OAAM;oBACN,WAAU;;sCAEV,sSAAC,2SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAA4B;;;;;;;;;;;;;;AAM7D;GAzCM;;QACe,gUAAA,CAAA,YAAS;;;KADxB;uCA2CS", "debugId": null}}, {"offset": {"line": 7633, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/editor/generative/ai-selector.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport { Command, CommandInput } from \"../../ui/command\";\r\n\r\nimport { ArrowUp } from \"lucide-react\";\r\nimport { useEditor } from \"novel\";\r\nimport { addA<PERSON><PERSON>ighlight } from \"novel\";\r\nimport { useCallback, useState } from \"react\";\r\nimport Markdown from \"react-markdown\";\r\nimport { toast } from \"sonner\";\r\nimport { Button } from \"../../ui/button\";\r\nimport Magic from \"../../ui/icons/magic\";\r\nimport { ScrollArea } from \"../../ui/scroll-area\";\r\nimport AICompletionCommands from \"./ai-completion-command\";\r\nimport AISelectorCommands from \"./ai-selector-commands\";\r\nimport { LoadingOutlined } from \"@ant-design/icons\";\r\nimport { resolveServiceURL } from \"~/core/api/resolve-service-url\";\r\nimport { fetchStream } from \"~/core/sse\";\r\n//TODO: I think it makes more sense to create a custom Tiptap extension for this functionality https://tiptap.dev/docs/editor/ai/introduction\r\n\r\ninterface AISelectorProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n}\r\n\r\nfunction useProseCompletion() {\r\n  const [completion, setCompletion] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<Error | null>(null);\r\n\r\n  const complete = useCallback(\r\n    async (prompt: string, options?: { body?: Record<string, any> }) => {\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n      try {\r\n        const response = await fetchStream(\r\n          resolveServiceURL(\"/api/prose/generate\"),\r\n          {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n            body: JSON.stringify({\r\n              prompt,\r\n              ...options?.body,\r\n            }),\r\n          },\r\n        );\r\n\r\n        let fullText = \"\";\r\n\r\n        // Process the streaming response\r\n        for await (const chunk of response) {\r\n          fullText += chunk.data;\r\n          setCompletion(fullText);\r\n        }\r\n\r\n        setIsLoading(false);\r\n        return fullText;\r\n      } catch (e) {\r\n        const error = e instanceof Error ? e : new Error(\"An error occurred\");\r\n        setError(error);\r\n        toast.error(error.message);\r\n        setIsLoading(false);\r\n        throw error;\r\n      }\r\n    },\r\n    [],\r\n  );\r\n\r\n  const reset = useCallback(() => {\r\n    setCompletion(\"\");\r\n    setError(null);\r\n    setIsLoading(false);\r\n  }, []);\r\n\r\n  return {\r\n    completion,\r\n    complete,\r\n    isLoading,\r\n    error,\r\n    reset,\r\n  };\r\n}\r\n\r\nexport function AISelector({ onOpenChange }: AISelectorProps) {\r\n  const { editor } = useEditor();\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n\r\n  const { completion, complete, isLoading } = useProseCompletion();\r\n\r\n  if (!editor) return null;\r\n\r\n  const hasCompletion = completion.length > 0;\r\n\r\n  return (\r\n    <Command className=\"w-[350px]\">\r\n      {hasCompletion && (\r\n        <div className=\"flex max-h-[400px]\">\r\n          <ScrollArea>\r\n            <div className=\"prose prose-sm dark:prose-invert p-2 px-4\">\r\n              <Markdown>{completion}</Markdown>\r\n            </div>\r\n          </ScrollArea>\r\n        </div>\r\n      )}\r\n\r\n      {isLoading && (\r\n        <div className=\"flex h-12 w-full items-center px-4 text-sm font-medium text-purple-500\">\r\n          <Magic className=\"mr-2 h-4 w-4 shrink-0\" />\r\n          AI is thinking\r\n          <div className=\"mt-1 ml-2\">\r\n            <LoadingOutlined />\r\n          </div>\r\n        </div>\r\n      )}\r\n      {!isLoading && (\r\n        <>\r\n          <div className=\"relative\">\r\n            <CommandInput\r\n              value={inputValue}\r\n              onValueChange={setInputValue}\r\n              autoFocus\r\n              placeholder={\r\n                hasCompletion\r\n                  ? \"Tell AI what to do next\"\r\n                  : \"Ask AI to edit or generate...\"\r\n              }\r\n              onFocus={() => addAIHighlight(editor)}\r\n            />\r\n            <Button\r\n              size=\"icon\"\r\n              className=\"absolute top-1/2 right-2 h-6 w-6 -translate-y-1/2 rounded-full bg-purple-500 hover:bg-purple-900\"\r\n              onClick={() => {\r\n                if (completion)\r\n                  return complete(completion, {\r\n                    body: { option: \"zap\", command: inputValue },\r\n                  }).then(() => setInputValue(\"\"));\r\n\r\n                const slice = editor.state.selection.content();\r\n                const text = editor.storage.markdown.serializer.serialize(\r\n                  slice.content,\r\n                );\r\n\r\n                complete(text, {\r\n                  body: { option: \"zap\", command: inputValue },\r\n                }).then(() => setInputValue(\"\"));\r\n              }}\r\n            >\r\n              <ArrowUp className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n          {hasCompletion ? (\r\n            <AICompletionCommands\r\n              onDiscard={() => {\r\n                editor.chain().unsetHighlight().focus().run();\r\n                onOpenChange(false);\r\n              }}\r\n              completion={completion}\r\n            />\r\n          ) : (\r\n            <AISelectorCommands\r\n              onSelect={(value, option) =>\r\n                complete(value, { body: { option } })\r\n              }\r\n            />\r\n          )}\r\n        </>\r\n      )}\r\n    </Command>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAjBA;;;;;;;;;;;;;;;;AAyBA,SAAS;;IACP,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;oDACzB,OAAO,QAAgB;YACrB,aAAa;YACb,SAAS;YAET,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,cAAW,AAAD,EAC/B,CAAA,GAAA,kJAAA,CAAA,oBAAiB,AAAD,EAAE,wBAClB;oBACE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA,GAAG,SAAS,IAAI;oBAClB;gBACF;gBAGF,IAAI,WAAW;gBAEf,iCAAiC;gBACjC,WAAW,MAAM,SAAS,SAAU;oBAClC,YAAY,MAAM,IAAI;oBACtB,cAAc;gBAChB;gBAEA,aAAa;gBACb,OAAO;YACT,EAAE,OAAO,GAAG;gBACV,MAAM,QAAQ,aAAa,QAAQ,IAAI,IAAI,MAAM;gBACjD,SAAS;gBACT,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO;gBACzB,aAAa;gBACb,MAAM;YACR;QACF;mDACA,EAAE;IAGJ,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;iDAAE;YACxB,cAAc;YACd,SAAS;YACT,aAAa;QACf;gDAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;GA3DS;AA6DF,SAAS,WAAW,EAAE,YAAY,EAAmB;;IAC1D,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAE5C,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,gBAAgB,WAAW,MAAM,GAAG;IAE1C,qBACE,sSAAC,sIAAA,CAAA,UAAO;QAAC,WAAU;;YAChB,+BACC,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,6IAAA,CAAA,aAAU;8BACT,cAAA,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC,mTAAA,CAAA,UAAQ;sCAAE;;;;;;;;;;;;;;;;;;;;;YAMlB,2BACC,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,6IAAA,CAAA,UAAK;wBAAC,WAAU;;;;;;oBAA0B;kCAE3C,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC,qUAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;YAIrB,CAAC,2BACA;;kCACE,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,sIAAA,CAAA,eAAY;gCACX,OAAO;gCACP,eAAe;gCACf,SAAS;gCACT,aACE,gBACI,4BACA;gCAEN,SAAS,IAAM,CAAA,GAAA,kQAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;0CAEhC,sSAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,SAAS;oCACP,IAAI,YACF,OAAO,SAAS,YAAY;wCAC1B,MAAM;4CAAE,QAAQ;4CAAO,SAAS;wCAAW;oCAC7C,GAAG,IAAI,CAAC,IAAM,cAAc;oCAE9B,MAAM,QAAQ,OAAO,KAAK,CAAC,SAAS,CAAC,OAAO;oCAC5C,MAAM,OAAO,OAAO,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CACvD,MAAM,OAAO;oCAGf,SAAS,MAAM;wCACb,MAAM;4CAAE,QAAQ;4CAAO,SAAS;wCAAW;oCAC7C,GAAG,IAAI,CAAC,IAAM,cAAc;gCAC9B;0CAEA,cAAA,sSAAC,mSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAGtB,8BACC,sSAAC,4KAAA,CAAA,UAAoB;wBACnB,WAAW;4BACT,OAAO,KAAK,GAAG,cAAc,GAAG,KAAK,GAAG,GAAG;4BAC3C,aAAa;wBACf;wBACA,YAAY;;;;;6CAGd,sSAAC,2KAAA,CAAA,UAAkB;wBACjB,UAAU,CAAC,OAAO,SAChB,SAAS,OAAO;gCAAE,MAAM;oCAAE;gCAAO;4BAAE;;;;;;;;;;;;;;AAQnD;IAtFgB;;QACK,gUAAA,CAAA,YAAS;QAGgB;;;KAJ9B", "debugId": null}}, {"offset": {"line": 7893, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/editor/generative/generative-menu-switch.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { EditorBubble, removeAIHighlight, useEditor } from \"novel\";\r\nimport { Fragment, type ReactNode, useEffect } from \"react\";\r\nimport { Button } from \"../../ui/button\";\r\nimport Magic from \"../../ui/icons/magic\";\r\nimport { AISelector } from \"./ai-selector\";\r\nimport { useReplay } from \"~/core/replay\";\r\nimport { TooltipContent, TooltipTrigger } from \"~/components/ui/tooltip\";\r\nimport { Tooltip } from \"~/components/ui/tooltip\";\r\n\r\ninterface GenerativeMenuSwitchProps {\r\n  children: ReactNode;\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n}\r\nconst GenerativeMenuSwitch = ({\r\n  children,\r\n  open,\r\n  onOpenChange,\r\n}: GenerativeMenuSwitchProps) => {\r\n  const { editor } = useEditor();\r\n  const { isReplay } = useReplay();\r\n  useEffect(() => {\r\n    if (!open && editor) removeAIHighlight(editor);\r\n  }, [open]);\r\n\r\n  if (!editor) return null;\r\n  return (\r\n    <EditorBubble\r\n      tippyOptions={{\r\n        placement: open ? \"bottom-start\" : \"top\",\r\n        onHidden: () => {\r\n          onOpenChange(false);\r\n          editor.chain().unsetHighlight().run();\r\n        },\r\n      }}\r\n      className=\"border-muted bg-background flex w-fit max-w-[90vw] overflow-hidden rounded-md border shadow-xl\"\r\n    >\r\n      {open && <AISelector open={open} onOpenChange={onOpenChange} />}\r\n      {!open && (\r\n        <Fragment>\r\n          {isReplay ? (\r\n            <Tooltip>\r\n              <TooltipTrigger>\r\n                <Button\r\n                  className=\"gap-1 rounded-none text-purple-500\"\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  disabled\r\n                >\r\n                  <Magic className=\"h-5 w-5\" />\r\n                  Ask AI\r\n                </Button>\r\n              </TooltipTrigger>\r\n              <TooltipContent>You can't ask AI in replay mode.</TooltipContent>\r\n            </Tooltip>\r\n          ) : (\r\n            <Button\r\n              className=\"gap-1 rounded-none text-purple-500\"\r\n              variant=\"ghost\"\r\n              onClick={() => onOpenChange(true)}\r\n              size=\"sm\"\r\n            >\r\n              <Magic className=\"h-5 w-5\" />\r\n              Ask AI\r\n            </Button>\r\n          )}\r\n          {children}\r\n        </Fragment>\r\n      )}\r\n    </EditorBubble>\r\n  );\r\n};\r\n\r\nexport default GenerativeMenuSwitch;\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;;;AAQA,MAAM,uBAAuB,CAAC,EAC5B,QAAQ,EACR,IAAI,EACJ,YAAY,EACc;;IAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IAC7B,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,CAAC,QAAQ,QAAQ,CAAA,GAAA,kQAAA,CAAA,oBAAiB,AAAD,EAAE;QACzC;yCAAG;QAAC;KAAK;IAET,IAAI,CAAC,QAAQ,OAAO;IACpB,qBACE,sSAAC,kQAAA,CAAA,eAAY;QACX,cAAc;YACZ,WAAW,OAAO,iBAAiB;YACnC,UAAU;gBACR,aAAa;gBACb,OAAO,KAAK,GAAG,cAAc,GAAG,GAAG;YACrC;QACF;QACA,WAAU;;YAET,sBAAQ,sSAAC,+JAAA,CAAA,aAAU;gBAAC,MAAM;gBAAM,cAAc;;;;;;YAC9C,CAAC,sBACA,sSAAC,sQAAA,CAAA,WAAQ;;oBACN,yBACC,sSAAC,sIAAA,CAAA,UAAO;;0CACN,sSAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAQ;oCACR,MAAK;oCACL,QAAQ;;sDAER,sSAAC,6IAAA,CAAA,UAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIjC,sSAAC,sIAAA,CAAA,iBAAc;0CAAC;;;;;;;;;;;6CAGlB,sSAAC,qIAAA,CAAA,SAAM;wBACL,WAAU;wBACV,SAAQ;wBACR,SAAS,IAAM,aAAa;wBAC5B,MAAK;;0CAEL,sSAAC,6IAAA,CAAA,UAAK;gCAAC,WAAU;;;;;;4BAAY;;;;;;;oBAIhC;;;;;;;;;;;;;AAKX;GAzDM;;QAKe,gUAAA,CAAA,YAAS;QACP,iIAAA,CAAA,YAAS;;;KAN1B;uCA2DS", "debugId": null}}, {"offset": {"line": 8043, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/editor/image-upload.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { createImageUpload } from \"novel\";\r\nimport { toast } from \"sonner\";\r\n\r\nconst onUpload = (file: File) => {\r\n  const promise = fetch(\"/api/upload\", {\r\n    method: \"POST\",\r\n    headers: {\r\n      \"content-type\": file?.type || \"application/octet-stream\",\r\n      \"x-vercel-filename\": file?.name || \"image.png\",\r\n    },\r\n    body: file,\r\n  });\r\n\r\n  return new Promise((resolve, reject) => {\r\n    toast.promise(\r\n      promise.then(async (res) => {\r\n        // Successfully uploaded image\r\n        if (res.status === 200) {\r\n          const { url } = (await res.json()) as { url: string };\r\n          // preload the image\r\n          const image = new Image();\r\n          image.src = url;\r\n          image.onload = () => {\r\n            resolve(url);\r\n          };\r\n          // No blob store configured\r\n        } else if (res.status === 401) {\r\n          resolve(file);\r\n          throw new Error(\r\n            \"`BLOB_READ_WRITE_TOKEN` environment variable not found, reading image locally instead.\",\r\n          );\r\n          // Unknown error\r\n        } else {\r\n          throw new Error(\"Error uploading image. Please try again.\");\r\n        }\r\n      }),\r\n      {\r\n        loading: \"Uploading image...\",\r\n        success: \"Image uploaded successfully.\",\r\n        error: (e) => {\r\n          reject(e);\r\n          return e.message;\r\n        },\r\n      },\r\n    );\r\n  });\r\n};\r\n\r\nexport const uploadFn = createImageUpload({\r\n  onUpload,\r\n  validateFn: (file) => {\r\n    if (!file.type.includes(\"image/\")) {\r\n      toast.error(\"File type not supported.\");\r\n      return false;\r\n    }\r\n    if (file.size / 1024 / 1024 > 20) {\r\n      toast.error(\"File size too big (max 20MB).\");\r\n      return false;\r\n    }\r\n    return true;\r\n  },\r\n});\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;AACA;;;AAEA,MAAM,WAAW,CAAC;IAChB,MAAM,UAAU,MAAM,eAAe;QACnC,QAAQ;QACR,SAAS;YACP,gBAAgB,MAAM,QAAQ;YAC9B,qBAAqB,MAAM,QAAQ;QACrC;QACA,MAAM;IACR;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,2QAAA,CAAA,QAAK,CAAC,OAAO,CACX,QAAQ,IAAI,CAAC,OAAO;YAClB,8BAA8B;YAC9B,IAAI,IAAI,MAAM,KAAK,KAAK;gBACtB,MAAM,EAAE,GAAG,EAAE,GAAI,MAAM,IAAI,IAAI;gBAC/B,oBAAoB;gBACpB,MAAM,QAAQ,IAAI;gBAClB,MAAM,GAAG,GAAG;gBACZ,MAAM,MAAM,GAAG;oBACb,QAAQ;gBACV;YACA,2BAA2B;YAC7B,OAAO,IAAI,IAAI,MAAM,KAAK,KAAK;gBAC7B,QAAQ;gBACR,MAAM,IAAI,MACR;YAEF,gBAAgB;YAClB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,IACA;YACE,SAAS;YACT,SAAS;YACT,OAAO,CAAC;gBACN,OAAO;gBACP,OAAO,EAAE,OAAO;YAClB;QACF;IAEJ;AACF;AAEO,MAAM,WAAW,CAAA,GAAA,kQAAA,CAAA,oBAAiB,AAAD,EAAE;IACxC;IACA,YAAY,CAAC;QACX,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,WAAW;YACjC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;QACA,IAAI,KAAK,IAAI,GAAG,OAAO,OAAO,IAAI;YAChC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 8113, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/editor/selectors/text-buttons.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Button } from \"../../ui/button\";\r\nimport { cn } from \"../../../lib/utils\";\r\nimport {\r\n  BoldIcon,\r\n  CodeIcon,\r\n  ItalicIcon,\r\n  StrikethroughIcon,\r\n  UnderlineIcon,\r\n} from \"lucide-react\";\r\nimport { EditorBubbleItem, useEditor } from \"novel\";\r\nimport type { SelectorItem } from \"./node-selector\";\r\n\r\nexport const TextButtons = () => {\r\n  const { editor } = useEditor();\r\n  if (!editor) return null;\r\n  const items: SelectorItem[] = [\r\n    {\r\n      name: \"bold\",\r\n      isActive: (editor) => editor.isActive(\"bold\"),\r\n      command: (editor) => editor.chain().focus().toggleBold().run(),\r\n      icon: BoldIcon,\r\n    },\r\n    {\r\n      name: \"italic\",\r\n      isActive: (editor) => editor.isActive(\"italic\"),\r\n      command: (editor) => editor.chain().focus().toggleItalic().run(),\r\n      icon: ItalicIcon,\r\n    },\r\n    {\r\n      name: \"underline\",\r\n      isActive: (editor) => editor.isActive(\"underline\"),\r\n      command: (editor) => editor.chain().focus().toggleUnderline().run(),\r\n      icon: UnderlineIcon,\r\n    },\r\n    {\r\n      name: \"strike\",\r\n      isActive: (editor) => editor.isActive(\"strike\"),\r\n      command: (editor) => editor.chain().focus().toggleStrike().run(),\r\n      icon: StrikethroughIcon,\r\n    },\r\n    {\r\n      name: \"code\",\r\n      isActive: (editor) => editor.isActive(\"code\"),\r\n      command: (editor) => editor.chain().focus().toggleCode().run(),\r\n      icon: CodeIcon,\r\n    },\r\n  ];\r\n  return (\r\n    <div className=\"flex\">\r\n      {items.map((item) => (\r\n        <EditorBubbleItem\r\n          key={item.name}\r\n          onSelect={(editor) => {\r\n            item.command(editor);\r\n          }}\r\n        >\r\n          <Button\r\n            size=\"sm\"\r\n            className=\"rounded-none\"\r\n            variant=\"ghost\"\r\n            type=\"button\"\r\n          >\r\n            <item.icon\r\n              className={cn(\"h-4 w-4\", {\r\n                \"text-blue-500\": item.isActive(editor),\r\n              })}\r\n            />\r\n          </Button>\r\n        </EditorBubbleItem>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAAA;;;;;;;AAGO,MAAM,cAAc;;IACzB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAC3B,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,QAAwB;QAC5B;YACE,MAAM;YACN,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;YACtC,SAAS,CAAC,SAAW,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;YAC5D,MAAM,6RAAA,CAAA,WAAQ;QAChB;QACA;YACE,MAAM;YACN,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;YACtC,SAAS,CAAC,SAAW,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;YAC9D,MAAM,iSAAA,CAAA,aAAU;QAClB;QACA;YACE,MAAM;YACN,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;YACtC,SAAS,CAAC,SAAW,OAAO,KAAK,GAAG,KAAK,GAAG,eAAe,GAAG,GAAG;YACjE,MAAM,uSAAA,CAAA,gBAAa;QACrB;QACA;YACE,MAAM;YACN,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;YACtC,SAAS,CAAC,SAAW,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;YAC9D,MAAM,+SAAA,CAAA,oBAAiB;QACzB;QACA;YACE,MAAM;YACN,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;YACtC,SAAS,CAAC,SAAW,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;YAC5D,MAAM,6RAAA,CAAA,WAAQ;QAChB;KACD;IACD,qBACE,sSAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,qBACV,sSAAC,kQAAA,CAAA,mBAAgB;gBAEf,UAAU,CAAC;oBACT,KAAK,OAAO,CAAC;gBACf;0BAEA,cAAA,sSAAC,qIAAA,CAAA,SAAM;oBACL,MAAK;oBACL,WAAU;oBACV,SAAQ;oBACR,MAAK;8BAEL,cAAA,sSAAC,KAAK,IAAI;wBACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;4BACvB,iBAAiB,KAAK,QAAQ,CAAC;wBACjC;;;;;;;;;;;eAdC,KAAK,IAAI;;;;;;;;;;AAqBxB;GA5Da;;QACQ,gUAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 8223, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/editor/slash-command.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport {\r\n  CheckSquare,\r\n  Code,\r\n  Heading1,\r\n  Heading2,\r\n  Heading3,\r\n  List,\r\n  ListOrdered,\r\n  Text,\r\n  TextQuote,\r\n} from \"lucide-react\";\r\nimport { Command, createSuggestionItems, renderItems } from \"novel\";\r\n// import { uploadFn } from \"./image-upload\";\r\n\r\nexport const suggestionItems = createSuggestionItems([\r\n  {\r\n    title: \"Text\",\r\n    description: \"Just start typing with plain text.\",\r\n    searchTerms: [\"p\", \"paragraph\"],\r\n    icon: <Text size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .toggleNode(\"paragraph\", \"paragraph\")\r\n        .run();\r\n    },\r\n  },\r\n  {\r\n    title: \"To-do List\",\r\n    description: \"Track tasks with a to-do list.\",\r\n    searchTerms: [\"todo\", \"task\", \"list\", \"check\", \"checkbox\"],\r\n    icon: <CheckSquare size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor.chain().focus().deleteRange(range).toggleTaskList().run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Heading 1\",\r\n    description: \"Big section heading.\",\r\n    searchTerms: [\"title\", \"big\", \"large\"],\r\n    icon: <Heading1 size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .setNode(\"heading\", { level: 1 })\r\n        .run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Heading 2\",\r\n    description: \"Medium section heading.\",\r\n    searchTerms: [\"subtitle\", \"medium\"],\r\n    icon: <Heading2 size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .setNode(\"heading\", { level: 2 })\r\n        .run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Heading 3\",\r\n    description: \"Small section heading.\",\r\n    searchTerms: [\"subtitle\", \"small\"],\r\n    icon: <Heading3 size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .setNode(\"heading\", { level: 3 })\r\n        .run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Bullet List\",\r\n    description: \"Create a simple bullet list.\",\r\n    searchTerms: [\"unordered\", \"point\"],\r\n    icon: <List size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor.chain().focus().deleteRange(range).toggleBulletList().run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Numbered List\",\r\n    description: \"Create a list with numbering.\",\r\n    searchTerms: [\"ordered\"],\r\n    icon: <ListOrdered size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor.chain().focus().deleteRange(range).toggleOrderedList().run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Quote\",\r\n    description: \"Capture a quote.\",\r\n    searchTerms: [\"blockquote\"],\r\n    icon: <TextQuote size={18} />,\r\n    command: ({ editor, range }) =>\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .toggleNode(\"paragraph\", \"paragraph\")\r\n        .toggleBlockquote()\r\n        .run(),\r\n  },\r\n  {\r\n    title: \"Code\",\r\n    description: \"Capture a code snippet.\",\r\n    searchTerms: [\"codeblock\"],\r\n    icon: <Code size={18} />,\r\n    command: ({ editor, range }) =>\r\n      editor.chain().focus().deleteRange(range).toggleCodeBlock().run(),\r\n  },\r\n  // {\r\n  //   title: \"Image\",\r\n  //   description: \"Upload an image from your computer.\",\r\n  //   searchTerms: [\"photo\", \"picture\", \"media\"],\r\n  //   icon: <ImageIcon size={18} />,\r\n  //   command: ({ editor, range }) => {\r\n  //     editor.chain().focus().deleteRange(range).run();\r\n  //     // upload image\r\n  //     const input = document.createElement(\"input\");\r\n  //     input.type = \"file\";\r\n  //     input.accept = \"image/*\";\r\n  //     input.onchange = async () => {\r\n  //       if (input.files?.length) {\r\n  //         const file = input.files[0];\r\n  //         if (!file) return;\r\n  //         const pos = editor.view.state.selection.from;\r\n  //         uploadFn(file, editor.view, pos);\r\n  //       }\r\n  //     };\r\n  //     input.click();\r\n  //   },\r\n  // },\r\n  // {\r\n  //   title: \"Youtube\",\r\n  //   description: \"Embed a Youtube video.\",\r\n  //   searchTerms: [\"video\", \"youtube\", \"embed\"],\r\n  //   icon: <Youtube size={18} />,\r\n  //   command: ({ editor, range }) => {\r\n  //     const videoLink = prompt(\"Please enter Youtube Video Link\");\r\n  //     //From https://regexr.com/3dj5t\r\n  //     const ytRegex = new RegExp(\r\n  //       /^((?:https?:)?\\/\\/)?((?:www|m)\\.)?((?:youtube\\.com|youtu.be))(\\/(?:[\\w\\-]+\\?v=|embed\\/|v\\/)?)([\\w\\-]+)(\\S+)?$/,\r\n  //     );\r\n\r\n  //     if (videoLink && ytRegex.test(videoLink)) {\r\n  //       editor\r\n  //         .chain()\r\n  //         .focus()\r\n  //         .deleteRange(range)\r\n  //         .setYoutubeVideo({\r\n  //           src: videoLink,\r\n  //         })\r\n  //         .run();\r\n  //     } else {\r\n  //       if (videoLink !== null) {\r\n  //         alert(\"Please enter a correct Youtube Video Link\");\r\n  //       }\r\n  //     }\r\n  //   },\r\n  // },\r\n  // {\r\n  //   title: \"Twitter\",\r\n  //   description: \"Embed a Tweet.\",\r\n  //   searchTerms: [\"twitter\", \"embed\"],\r\n  //   icon: <Twitter size={18} />,\r\n  //   command: ({ editor, range }) => {\r\n  //     const tweetLink = prompt(\"Please enter Twitter Link\");\r\n  //     const tweetRegex = new RegExp(\r\n  //       /^https?:\\/\\/(www\\.)?x\\.com\\/([a-zA-Z0-9_]{1,15})(\\/status\\/(\\d+))?(\\/\\S*)?$/,\r\n  //     );\r\n\r\n  //     if (tweetLink && tweetRegex.test(tweetLink)) {\r\n  //       editor\r\n  //         .chain()\r\n  //         .focus()\r\n  //         .deleteRange(range)\r\n  //         .setTweet({\r\n  //           src: tweetLink,\r\n  //         })\r\n  //         .run();\r\n  //     } else {\r\n  //       if (tweetLink !== null) {\r\n  //         alert(\"Please enter a correct Twitter Link\");\r\n  //       }\r\n  //     }\r\n  //   },\r\n  // },\r\n]);\r\n\r\nexport const slashCommand = Command.configure({\r\n  suggestion: {\r\n    items: () => suggestionItems,\r\n    render: renderItems,\r\n  },\r\n});\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;AAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;;AAGO,MAAM,kBAAkB,CAAA,GAAA,kQAAA,CAAA,wBAAqB,AAAD,EAAE;IACnD;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAK;SAAY;QAC/B,oBAAM,sSAAC,yRAAA,CAAA,OAAI;YAAC,MAAM;;;;;;QAClB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,UAAU,CAAC,aAAa,aACxB,GAAG;QACR;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAQ;YAAQ;YAAQ;YAAS;SAAW;QAC1D,oBAAM,sSAAC,kTAAA,CAAA,cAAW;YAAC,MAAM;;;;;;QACzB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,OAAO,cAAc,GAAG,GAAG;QAChE;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAS;YAAO;SAAQ;QACtC,oBAAM,sSAAC,qSAAA,CAAA,WAAQ;YAAC,MAAM;;;;;;QACtB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,OAAO,CAAC,WAAW;gBAAE,OAAO;YAAE,GAC9B,GAAG;QACR;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAY;SAAS;QACnC,oBAAM,sSAAC,qSAAA,CAAA,WAAQ;YAAC,MAAM;;;;;;QACtB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,OAAO,CAAC,WAAW;gBAAE,OAAO;YAAE,GAC9B,GAAG;QACR;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAY;SAAQ;QAClC,oBAAM,sSAAC,qSAAA,CAAA,WAAQ;YAAC,MAAM;;;;;;QACtB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,OAAO,CAAC,WAAW;gBAAE,OAAO;YAAE,GAC9B,GAAG;QACR;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAa;SAAQ;QACnC,oBAAM,sSAAC,yRAAA,CAAA,OAAI;YAAC,MAAM;;;;;;QAClB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,OAAO,gBAAgB,GAAG,GAAG;QAClE;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;SAAU;QACxB,oBAAM,sSAAC,2SAAA,CAAA,cAAW;YAAC,MAAM;;;;;;QACzB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,OAAO,iBAAiB,GAAG,GAAG;QACnE;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;SAAa;QAC3B,oBAAM,sSAAC,uSAAA,CAAA,YAAS;YAAC,MAAM;;;;;;QACvB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,UAAU,CAAC,aAAa,aACxB,gBAAgB,GAChB,GAAG;IACV;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;SAAY;QAC1B,oBAAM,sSAAC,yRAAA,CAAA,OAAI;YAAC,MAAM;;;;;;QAClB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GACzB,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,OAAO,eAAe,GAAG,GAAG;IACnE;CA8ED;AAEM,MAAM,eAAe,kQAAA,CAAA,UAAO,CAAC,SAAS,CAAC;IAC5C,YAAY;QACV,OAAO,IAAM;QACb,QAAQ,kQAAA,CAAA,cAAW;IACrB;AACF", "debugId": null}}, {"offset": {"line": 8425, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/components/editor/index.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport {\r\n  Editor<PERSON><PERSON><PERSON>,\r\n  EditorCommandEmpty,\r\n  Editor<PERSON><PERSON>mand<PERSON><PERSON>,\r\n  Editor<PERSON><PERSON><PERSON>List,\r\n  EditorContent,\r\n  type EditorInstance,\r\n  EditorRoot,\r\n  ImageResizer,\r\n  type JSONContent,\r\n  handleCommandNavigation,\r\n  handleImageDrop,\r\n  handleImagePaste,\r\n} from \"novel\";\r\nimport type { Content } from \"@tiptap/react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useDebouncedCallback } from \"use-debounce\";\r\nimport { defaultExtensions } from \"./extensions\";\r\nimport { ColorSelector } from \"./selectors/color-selector\";\r\nimport { LinkSelector } from \"./selectors/link-selector\";\r\nimport { MathSelector } from \"./selectors/math-selector\";\r\nimport { NodeSelector } from \"./selectors/node-selector\";\r\nimport { Separator } from \"../ui/separator\";\r\n\r\nimport GenerativeMenuSwitch from \"./generative/generative-menu-switch\";\r\nimport { uploadFn } from \"./image-upload\";\r\nimport { TextButtons } from \"./selectors/text-buttons\";\r\nimport { slashCommand, suggestionItems } from \"./slash-command\";\r\n// import { defaultEditorContent } from \"./content\";\r\n\r\nimport \"~/styles/prosemirror.css\";\r\n\r\nconst hljs = require(\"highlight.js\");\r\n\r\nconst extensions = [...defaultExtensions, slashCommand];\r\n\r\nexport interface ReportEditorProps {\r\n  content: Content;\r\n  onMarkdownChange?: (markdown: string) => void;\r\n}\r\n\r\nconst ReportEditor = ({ content, onMarkdownChange }: ReportEditorProps) => {\r\n  const [initialContent, setInitialContent] = useState<Content>(() => content);\r\n  const [saveStatus, setSaveStatus] = useState(\"Saved\");\r\n\r\n  const [openNode, setOpenNode] = useState(false);\r\n  const [openColor, setOpenColor] = useState(false);\r\n  const [openLink, setOpenLink] = useState(false);\r\n  const [openAI, setOpenAI] = useState(false);\r\n\r\n  //Apply Codeblock Highlighting on the HTML from editor.getHTML()\r\n  const highlightCodeblocks = (content: string) => {\r\n    const doc = new DOMParser().parseFromString(content, \"text/html\");\r\n    doc.querySelectorAll(\"pre code\").forEach((el) => {\r\n      // @ts-ignore\r\n      // https://highlightjs.readthedocs.io/en/latest/api.html?highlight=highlightElement#highlightelement\r\n      hljs.highlightElement(el);\r\n    });\r\n    return new XMLSerializer().serializeToString(doc);\r\n  };\r\n\r\n  const debouncedUpdates = useDebouncedCallback(\r\n    async (editor: EditorInstance) => {\r\n      if (onMarkdownChange) {\r\n        const markdown = editor.storage.markdown.getMarkdown();\r\n        onMarkdownChange(markdown);\r\n      }\r\n      setSaveStatus(\"Saved\");\r\n    },\r\n    500,\r\n  );\r\n\r\n  if (!initialContent) return null;\r\n\r\n  return (\r\n    <div className=\"relative w-full\">\r\n      <EditorRoot>\r\n        <EditorContent\r\n          immediatelyRender={false}\r\n          initialContent={initialContent as JSONContent}\r\n          extensions={extensions}\r\n          className=\"border-muted relative h-full w-full\"\r\n          editorProps={{\r\n            handleDOMEvents: {\r\n              keydown: (_view, event) => handleCommandNavigation(event),\r\n            },\r\n            handlePaste: (view, event) =>\r\n              handleImagePaste(view, event, uploadFn),\r\n            handleDrop: (view, event, _slice, moved) =>\r\n              handleImageDrop(view, event, moved, uploadFn),\r\n            attributes: {\r\n              class:\r\n                \"prose prose-base prose-p:my-4 dark:prose-invert prose-headings:font-title font-default focus:outline-none max-w-full\",\r\n            },\r\n          }}\r\n          onUpdate={({ editor }) => {\r\n            debouncedUpdates(editor);\r\n            setSaveStatus(\"Unsaved\");\r\n          }}\r\n          slotAfter={<ImageResizer />}\r\n        >\r\n          <EditorCommand className=\"border-muted bg-background z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border px-1 py-2 shadow-md transition-all\">\r\n            <EditorCommandEmpty className=\"text-muted-foreground px-2\">\r\n              No results\r\n            </EditorCommandEmpty>\r\n            <EditorCommandList>\r\n              {suggestionItems.map((item) => (\r\n                <EditorCommandItem\r\n                  value={item.title}\r\n                  onCommand={(val) => item.command?.(val)}\r\n                  className=\"hover:bg-accent aria-selected:bg-accent flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm\"\r\n                  key={item.title}\r\n                >\r\n                  <div className=\"border-muted bg-background flex h-10 w-10 items-center justify-center rounded-md border\">\r\n                    {item.icon}\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"font-medium\">{item.title}</p>\r\n                    <p className=\"text-muted-foreground text-xs\">\r\n                      {item.description}\r\n                    </p>\r\n                  </div>\r\n                </EditorCommandItem>\r\n              ))}\r\n            </EditorCommandList>\r\n          </EditorCommand>\r\n\r\n          <GenerativeMenuSwitch open={openAI} onOpenChange={setOpenAI}>\r\n            <Separator orientation=\"vertical\" />\r\n            <NodeSelector open={openNode} onOpenChange={setOpenNode} />\r\n            <Separator orientation=\"vertical\" />\r\n            <TextButtons />\r\n            <Separator orientation=\"vertical\" />\r\n            <ColorSelector open={openColor} onOpenChange={setOpenColor} />\r\n            <Separator orientation=\"vertical\" />\r\n            <LinkSelector open={openLink} onOpenChange={setOpenLink} />\r\n            <Separator orientation=\"vertical\" />\r\n            <MathSelector />\r\n          </GenerativeMenuSwitch>\r\n        </EditorContent>\r\n      </EditorRoot>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReportEditor;\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AA7BA;;;;;;;;;;;;;;;AAkCA,MAAM;AAEN,MAAM,aAAa;OAAI,6IAAA,CAAA,oBAAiB;IAAE,mJAAA,CAAA,eAAY;CAAC;AAOvD,MAAM,eAAe,CAAC,EAAE,OAAO,EAAE,gBAAgB,EAAqB;;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD;iCAAW,IAAM;;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,gEAAgE;IAChE,MAAM,sBAAsB,CAAC;QAC3B,MAAM,MAAM,IAAI,YAAY,eAAe,CAAC,SAAS;QACrD,IAAI,gBAAgB,CAAC,YAAY,OAAO,CAAC,CAAC;YACxC,aAAa;YACb,oGAAoG;YACpG,KAAK,gBAAgB,CAAC;QACxB;QACA,OAAO,IAAI,gBAAgB,iBAAiB,CAAC;IAC/C;IAEA,MAAM,mBAAmB,CAAA,GAAA,gPAAA,CAAA,uBAAoB,AAAD;+DAC1C,OAAO;YACL,IAAI,kBAAkB;gBACpB,MAAM,WAAW,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW;gBACpD,iBAAiB;YACnB;YACA,cAAc;QAChB;8DACA;IAGF,IAAI,CAAC,gBAAgB,OAAO;IAE5B,qBACE,sSAAC;QAAI,WAAU;kBACb,cAAA,sSAAC,kQAAA,CAAA,aAAU;sBACT,cAAA,sSAAC,kQAAA,CAAA,gBAAa;gBACZ,mBAAmB;gBACnB,gBAAgB;gBAChB,YAAY;gBACZ,WAAU;gBACV,aAAa;oBACX,iBAAiB;wBACf,SAAS,CAAC,OAAO,QAAU,CAAA,GAAA,kQAAA,CAAA,0BAAuB,AAAD,EAAE;oBACrD;oBACA,aAAa,CAAC,MAAM,QAClB,CAAA,GAAA,kQAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,OAAO,iJAAA,CAAA,WAAQ;oBACxC,YAAY,CAAC,MAAM,OAAO,QAAQ,QAChC,CAAA,GAAA,kQAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,OAAO,OAAO,iJAAA,CAAA,WAAQ;oBAC9C,YAAY;wBACV,OACE;oBACJ;gBACF;gBACA,UAAU,CAAC,EAAE,MAAM,EAAE;oBACnB,iBAAiB;oBACjB,cAAc;gBAChB;gBACA,yBAAW,sSAAC,kQAAA,CAAA,eAAY;;;;;;kCAExB,sSAAC,kQAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,sSAAC,kQAAA,CAAA,qBAAkB;gCAAC,WAAU;0CAA6B;;;;;;0CAG3D,sSAAC,kQAAA,CAAA,oBAAiB;0CACf,mJAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,qBACpB,sSAAC,kQAAA,CAAA,oBAAiB;wCAChB,OAAO,KAAK,KAAK;wCACjB,WAAW,CAAC,MAAQ,KAAK,OAAO,GAAG;wCACnC,WAAU;;0DAGV,sSAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,sSAAC;;kEACC,sSAAC;wDAAE,WAAU;kEAAe,KAAK,KAAK;;;;;;kEACtC,sSAAC;wDAAE,WAAU;kEACV,KAAK,WAAW;;;;;;;;;;;;;uCARhB,KAAK,KAAK;;;;;;;;;;;;;;;;kCAgBvB,sSAAC,6KAAA,CAAA,UAAoB;wBAAC,MAAM;wBAAQ,cAAc;;0CAChD,sSAAC,wIAAA,CAAA,YAAS;gCAAC,aAAY;;;;;;0CACvB,sSAAC,gKAAA,CAAA,eAAY;gCAAC,MAAM;gCAAU,cAAc;;;;;;0CAC5C,sSAAC,wIAAA,CAAA,YAAS;gCAAC,aAAY;;;;;;0CACvB,sSAAC,+JAAA,CAAA,cAAW;;;;;0CACZ,sSAAC,wIAAA,CAAA,YAAS;gCAAC,aAAY;;;;;;0CACvB,sSAAC,iKAAA,CAAA,gBAAa;gCAAC,MAAM;gCAAW,cAAc;;;;;;0CAC9C,sSAAC,wIAAA,CAAA,YAAS;gCAAC,aAAY;;;;;;0CACvB,sSAAC,gKAAA,CAAA,eAAY;gCAAC,MAAM;gCAAU,cAAc;;;;;;0CAC5C,sSAAC,wIAAA,CAAA,YAAS;gCAAC,aAAY;;;;;;0CACvB,sSAAC,gKAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzB;GAtGM;;QAoBqB,gPAAA,CAAA,uBAAoB;;;KApBzC;uCAwGS", "debugId": null}}, {"offset": {"line": 8704, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/app/chat/components/research-report-block.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { useCallback, useRef } from \"react\";\r\n\r\nimport { LoadingAnimation } from \"~/components/deer-flow/loading-animation\";\r\nimport { Markdown } from \"~/components/deer-flow/markdown\";\r\nimport ReportEditor from \"~/components/editor\";\r\nimport { useReplay } from \"~/core/replay\";\r\nimport { useMessage, useStore } from \"~/core/store\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport function ResearchReportBlock({\r\n  className,\r\n  messageId,\r\n  editing,\r\n}: {\r\n  className?: string;\r\n  researchId: string;\r\n  messageId: string;\r\n  editing: boolean;\r\n}) {\r\n  const message = useMessage(messageId);\r\n  const { isReplay } = useReplay();\r\n  const handleMarkdownChange = useCallback(\r\n    (markdown: string) => {\r\n      if (message) {\r\n        message.content = markdown;\r\n        useStore.setState({\r\n          messages: new Map(useStore.getState().messages).set(\r\n            message.id,\r\n            message,\r\n          ),\r\n        });\r\n      }\r\n    },\r\n    [message],\r\n  );\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const isCompleted = message?.isStreaming === false && message?.content !== \"\";\r\n  // TODO: scroll to top when completed, but it's not working\r\n  // useEffect(() => {\r\n  //   if (isCompleted && contentRef.current) {\r\n  //     setTimeout(() => {\r\n  //       contentRef\r\n  //         .current!.closest(\"[data-radix-scroll-area-viewport]\")\r\n  //         ?.scrollTo({\r\n  //           top: 0,\r\n  //           behavior: \"smooth\",\r\n  //         });\r\n  //     }, 500);\r\n  //   }\r\n  // }, [isCompleted]);\r\n\r\n  return (\r\n    <div ref={contentRef} className={cn(\"w-full pt-4 pb-8\", className)}>\r\n      {!isReplay && isCompleted && editing ? (\r\n        <ReportEditor\r\n          content={message?.content}\r\n          onMarkdownChange={handleMarkdownChange}\r\n        />\r\n      ) : (\r\n        <>\r\n          <Markdown animated checkLinkCredibility>\r\n            {message?.content}\r\n          </Markdown>\r\n          {message?.isStreaming && <LoadingAnimation className=\"my-12\" />}\r\n        </>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;;;;;;;;;;AAEO,SAAS,oBAAoB,EAClC,SAAS,EACT,SAAS,EACT,OAAO,EAMR;;IACC,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,uBAAuB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;iEACrC,CAAC;YACC,IAAI,SAAS;gBACX,QAAQ,OAAO,GAAG;gBAClB,gIAAA,CAAA,WAAQ,CAAC,QAAQ,CAAC;oBAChB,UAAU,IAAI,IAAI,gIAAA,CAAA,WAAQ,CAAC,QAAQ,GAAG,QAAQ,EAAE,GAAG,CACjD,QAAQ,EAAE,EACV;gBAEJ;YACF;QACF;gEACA;QAAC;KAAQ;IAEX,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,cAAc,SAAS,gBAAgB,SAAS,SAAS,YAAY;IAC3E,2DAA2D;IAC3D,oBAAoB;IACpB,6CAA6C;IAC7C,yBAAyB;IACzB,mBAAmB;IACnB,iEAAiE;IACjE,uBAAuB;IACvB,oBAAoB;IACpB,gCAAgC;IAChC,cAAc;IACd,eAAe;IACf,MAAM;IACN,qBAAqB;IAErB,qBACE,sSAAC;QAAI,KAAK;QAAY,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;kBACrD,CAAC,YAAY,eAAe,wBAC3B,sSAAC,wIAAA,CAAA,UAAY;YACX,SAAS,SAAS;YAClB,kBAAkB;;;;;iCAGpB;;8BACE,sSAAC,iJAAA,CAAA,WAAQ;oBAAC,QAAQ;oBAAC,oBAAoB;8BACpC,SAAS;;;;;;gBAEX,SAAS,6BAAe,sSAAC,6JAAA,CAAA,mBAAgB;oBAAC,WAAU;;;;;;;;;;;;;AAK/D;GA3DgB;;QAUE,gIAAA,CAAA,aAAU;QACL,iIAAA,CAAA,YAAS;;;KAXhB", "debugId": null}}, {"offset": {"line": 8813, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/app/chat/components/research-block.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Check, Copy, Headphones, Pencil, Undo2, X, Download } from \"lucide-react\";\r\nimport { useCallback, useEffect, useState } from \"react\";\r\n\r\nimport { ScrollContainer } from \"~/components/deer-flow/scroll-container\";\r\nimport { Tooltip } from \"~/components/deer-flow/tooltip\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Card } from \"~/components/ui/card\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"~/components/ui/tabs\";\r\nimport { useReplay } from \"~/core/replay\";\r\nimport { closeResearch, listenToPodcast, useStore } from \"~/core/store\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport { ResearchActivitiesBlock } from \"./research-activities-block\";\r\nimport { ResearchReportBlock } from \"./research-report-block\";\r\n\r\nexport function ResearchBlock({\r\n  className,\r\n  researchId = null,\r\n}: {\r\n  className?: string;\r\n  researchId: string | null;\r\n}) {\r\n  const reportId = useStore((state) =>\r\n    researchId ? state.researchReportIds.get(researchId) : undefined,\r\n  );\r\n  const [activeTab, setActiveTab] = useState(\"activities\");\r\n  const hasReport = useStore((state) =>\r\n    researchId ? state.researchReportIds.has(researchId) : false,\r\n  );\r\n  const reportStreaming = useStore((state) =>\r\n    reportId ? (state.messages.get(reportId)?.isStreaming ?? false) : false,\r\n  );\r\n  const { isReplay } = useReplay();\r\n  useEffect(() => {\r\n    if (hasReport) {\r\n      setActiveTab(\"report\");\r\n    }\r\n  }, [hasReport]);\r\n\r\n  const handleGeneratePodcast = useCallback(async () => {\r\n    if (!researchId) {\r\n      return;\r\n    }\r\n    await listenToPodcast(researchId);\r\n  }, [researchId]);\r\n\r\n  const [editing, setEditing] = useState(false);\r\n  const [copied, setCopied] = useState(false);\r\n  const handleCopy = useCallback(() => {\r\n    if (!reportId) {\r\n      return;\r\n    }\r\n    const report = useStore.getState().messages.get(reportId);\r\n    if (!report) {\r\n      return;\r\n    }\r\n    void navigator.clipboard.writeText(report.content);\r\n    setCopied(true);\r\n    setTimeout(() => {\r\n      setCopied(false);\r\n    }, 1000);\r\n  }, [reportId]);\r\n\r\n  // Download report as markdown\r\n  const handleDownload = useCallback(() => {\r\n    if (!reportId) {\r\n      return;\r\n    }\r\n    const report = useStore.getState().messages.get(reportId);\r\n    if (!report) {\r\n      return;\r\n    }\r\n    const now = new Date();\r\n    const pad = (n: number) => n.toString().padStart(2, '0');\r\n    const timestamp = `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(now.getDate())}_${pad(now.getHours())}-${pad(now.getMinutes())}-${pad(now.getSeconds())}`;\r\n    const filename = `research-report-${timestamp}.md`;\r\n    const blob = new Blob([report.content], { type: 'text/markdown' });\r\n    const url = URL.createObjectURL(blob);\r\n    const a = document.createElement('a');\r\n    a.href = url;\r\n    a.download = filename;\r\n    document.body.appendChild(a);\r\n    a.click();\r\n    setTimeout(() => {\r\n      document.body.removeChild(a);\r\n      URL.revokeObjectURL(url);\r\n    }, 0);\r\n  }, [reportId]);\r\n\r\n    \r\n  const handleEdit = useCallback(() => {\r\n    setEditing((editing) => !editing);\r\n  }, []);\r\n\r\n  // When the research id changes, set the active tab to activities\r\n  useEffect(() => {\r\n    if (!hasReport) {\r\n      setActiveTab(\"activities\");\r\n    }\r\n  }, [hasReport, researchId]);\r\n\r\n  return (\r\n    <div className={cn(\"h-full w-full\", className)}>\r\n      <Card className={cn(\"relative h-full w-full pt-4\", className)}>\r\n        <div className=\"absolute right-4 flex h-9 items-center justify-center\">\r\n          {hasReport && !reportStreaming && (\r\n            <>\r\n              <Tooltip title=\"Generate podcast\">\r\n                <Button\r\n                  className=\"text-gray-400\"\r\n                  size=\"icon\"\r\n                  variant=\"ghost\"\r\n                  disabled={isReplay}\r\n                  onClick={handleGeneratePodcast}\r\n                >\r\n                  <Headphones />\r\n                </Button>\r\n              </Tooltip>\r\n              <Tooltip title=\"Edit\">\r\n                <Button\r\n                  className=\"text-gray-400\"\r\n                  size=\"icon\"\r\n                  variant=\"ghost\"\r\n                  disabled={isReplay}\r\n                  onClick={handleEdit}\r\n                >\r\n                  {editing ? <Undo2 /> : <Pencil />}\r\n                </Button>\r\n              </Tooltip>\r\n              <Tooltip title=\"Copy\">\r\n                <Button\r\n                  className=\"text-gray-400\"\r\n                  size=\"icon\"\r\n                  variant=\"ghost\"\r\n                  onClick={handleCopy}\r\n                >\r\n                  {copied ? <Check /> : <Copy />}\r\n                </Button>\r\n              </Tooltip>\r\n              <Tooltip title=\"Download report as markdown\">\r\n                <Button\r\n                  className=\"text-gray-400\"\r\n                  size=\"icon\"\r\n                  variant=\"ghost\"\r\n                  onClick={handleDownload}\r\n                >\r\n                  <Download />\r\n                </Button>\r\n              </Tooltip>\r\n            </>\r\n          )}\r\n          <Tooltip title=\"Close\">\r\n            <Button\r\n              className=\"text-gray-400\"\r\n              size=\"sm\"\r\n              variant=\"ghost\"\r\n              onClick={() => {\r\n                closeResearch();\r\n              }}\r\n            >\r\n              <X />\r\n            </Button>\r\n          </Tooltip>\r\n        </div>\r\n        <Tabs\r\n          className=\"flex h-full w-full flex-col\"\r\n          value={activeTab}\r\n          onValueChange={(value) => setActiveTab(value)}\r\n        >\r\n          <div className=\"flex w-full justify-center\">\r\n            <TabsList className=\"\">\r\n              <TabsTrigger\r\n                className=\"px-8\"\r\n                value=\"report\"\r\n                disabled={!hasReport}\r\n              >\r\n                Report\r\n              </TabsTrigger>\r\n              <TabsTrigger className=\"px-8\" value=\"activities\">\r\n                Activities\r\n              </TabsTrigger>\r\n            </TabsList>\r\n          </div>\r\n          <TabsContent\r\n            className=\"h-full min-h-0 flex-grow px-8\"\r\n            value=\"report\"\r\n            forceMount\r\n            hidden={activeTab !== \"report\"}\r\n          >\r\n            <ScrollContainer\r\n              className=\"px-5pb-20 h-full\"\r\n              scrollShadowColor=\"var(--card)\"\r\n              autoScrollToBottom={!hasReport || reportStreaming}\r\n            >\r\n              {reportId && researchId && (\r\n                <ResearchReportBlock\r\n                  className=\"mt-4\"\r\n                  researchId={researchId}\r\n                  messageId={reportId}\r\n                  editing={editing}\r\n                />\r\n              )}\r\n            </ScrollContainer>\r\n          </TabsContent>\r\n          <TabsContent\r\n            className=\"h-full min-h-0 flex-grow px-8\"\r\n            value=\"activities\"\r\n            forceMount\r\n            hidden={activeTab !== \"activities\"}\r\n          >\r\n            <ScrollContainer\r\n              className=\"h-full\"\r\n              scrollShadowColor=\"var(--card)\"\r\n              autoScrollToBottom={!hasReport || reportStreaming}\r\n            >\r\n              {researchId && (\r\n                <ResearchActivitiesBlock\r\n                  className=\"mt-4\"\r\n                  researchId={researchId}\r\n                />\r\n              )}\r\n            </ScrollContainer>\r\n          </TabsContent>\r\n        </Tabs>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAEA;AACA;;;;;;;;;;;;;;;AAEO,SAAS,cAAc,EAC5B,SAAS,EACT,aAAa,IAAI,EAIlB;;IACC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;4CAAE,CAAC,QACzB,aAAa,MAAM,iBAAiB,CAAC,GAAG,CAAC,cAAc;;IAEzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;6CAAE,CAAC,QAC1B,aAAa,MAAM,iBAAiB,CAAC,GAAG,CAAC,cAAc;;IAEzD,MAAM,kBAAkB,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;mDAAE,CAAC,QAChC,WAAY,MAAM,QAAQ,CAAC,GAAG,CAAC,WAAW,eAAe,QAAS;;IAEpE,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IAC7B,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,WAAW;gBACb,aAAa;YACf;QACF;kCAAG;QAAC;KAAU;IAEd,MAAM,wBAAwB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;4DAAE;YACxC,IAAI,CAAC,YAAY;gBACf;YACF;YACA,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE;QACxB;2DAAG;QAAC;KAAW;IAEf,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;iDAAE;YAC7B,IAAI,CAAC,UAAU;gBACb;YACF;YACA,MAAM,SAAS,gIAAA,CAAA,WAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC;YAChD,IAAI,CAAC,QAAQ;gBACX;YACF;YACA,KAAK,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,OAAO;YACjD,UAAU;YACV;yDAAW;oBACT,UAAU;gBACZ;wDAAG;QACL;gDAAG;QAAC;KAAS;IAEb,8BAA8B;IAC9B,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;qDAAE;YACjC,IAAI,CAAC,UAAU;gBACb;YACF;YACA,MAAM,SAAS,gIAAA,CAAA,WAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC;YAChD,IAAI,CAAC,QAAQ;gBACX;YACF;YACA,MAAM,MAAM,IAAI;YAChB,MAAM;iEAAM,CAAC,IAAc,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;;YACpD,MAAM,YAAY,GAAG,IAAI,WAAW,GAAG,CAAC,EAAE,IAAI,IAAI,QAAQ,KAAK,GAAG,CAAC,EAAE,IAAI,IAAI,OAAO,IAAI,CAAC,EAAE,IAAI,IAAI,QAAQ,IAAI,CAAC,EAAE,IAAI,IAAI,UAAU,IAAI,CAAC,EAAE,IAAI,IAAI,UAAU,KAAK;YAClK,MAAM,WAAW,CAAC,gBAAgB,EAAE,UAAU,GAAG,CAAC;YAClD,MAAM,OAAO,IAAI,KAAK;gBAAC,OAAO,OAAO;aAAC,EAAE;gBAAE,MAAM;YAAgB;YAChE,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG;YACb,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP;6DAAW;oBACT,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC1B,IAAI,eAAe,CAAC;gBACtB;4DAAG;QACL;oDAAG;QAAC;KAAS;IAGb,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;iDAAE;YAC7B;yDAAW,CAAC,UAAY,CAAC;;QAC3B;gDAAG,EAAE;IAEL,iEAAiE;IACjE,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW;gBACd,aAAa;YACf;QACF;kCAAG;QAAC;QAAW;KAAW;IAE1B,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBAClC,cAAA,sSAAC,mIAAA,CAAA,OAAI;YAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;;8BACjD,sSAAC;oBAAI,WAAU;;wBACZ,aAAa,CAAC,iCACb;;8CACE,sSAAC,gJAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,UAAU;wCACV,SAAS;kDAET,cAAA,sSAAC,qSAAA,CAAA,aAAU;;;;;;;;;;;;;;;8CAGf,sSAAC,gJAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,UAAU;wCACV,SAAS;kDAER,wBAAU,sSAAC,+RAAA,CAAA,QAAK;;;;iEAAM,sSAAC,6RAAA,CAAA,SAAM;;;;;;;;;;;;;;;8CAGlC,sSAAC,gJAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,SAAS;kDAER,uBAAS,sSAAC,2RAAA,CAAA,QAAK;;;;iEAAM,sSAAC,yRAAA,CAAA,OAAI;;;;;;;;;;;;;;;8CAG/B,sSAAC,gJAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,SAAS;kDAET,cAAA,sSAAC,iSAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;sCAKjB,sSAAC,gJAAA,CAAA,UAAO;4BAAC,OAAM;sCACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,MAAK;gCACL,SAAQ;gCACR,SAAS;oCACP,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;gCACd;0CAEA,cAAA,sSAAC,mRAAA,CAAA,IAAC;;;;;;;;;;;;;;;;;;;;;8BAIR,sSAAC,mIAAA,CAAA,OAAI;oBACH,WAAU;oBACV,OAAO;oBACP,eAAe,CAAC,QAAU,aAAa;;sCAEvC,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,mIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,sSAAC,mIAAA,CAAA,cAAW;wCACV,WAAU;wCACV,OAAM;wCACN,UAAU,CAAC;kDACZ;;;;;;kDAGD,sSAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;wCAAO,OAAM;kDAAa;;;;;;;;;;;;;;;;;sCAKrD,sSAAC,mIAAA,CAAA,cAAW;4BACV,WAAU;4BACV,OAAM;4BACN,UAAU;4BACV,QAAQ,cAAc;sCAEtB,cAAA,sSAAC,4JAAA,CAAA,kBAAe;gCACd,WAAU;gCACV,mBAAkB;gCAClB,oBAAoB,CAAC,aAAa;0CAEjC,YAAY,4BACX,sSAAC,mKAAA,CAAA,sBAAmB;oCAClB,WAAU;oCACV,YAAY;oCACZ,WAAW;oCACX,SAAS;;;;;;;;;;;;;;;;sCAKjB,sSAAC,mIAAA,CAAA,cAAW;4BACV,WAAU;4BACV,OAAM;4BACN,UAAU;4BACV,QAAQ,cAAc;sCAEtB,cAAA,sSAAC,4JAAA,CAAA,kBAAe;gCACd,WAAU;gCACV,mBAAkB;gCAClB,oBAAoB,CAAC,aAAa;0CAEjC,4BACC,sSAAC,uKAAA,CAAA,0BAAuB;oCACtB,WAAU;oCACV,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9B;GApNgB;;QAOG,gIAAA,CAAA,WAAQ;QAIP,gIAAA,CAAA,WAAQ;QAGF,gIAAA,CAAA,WAAQ;QAGX,iIAAA,CAAA,YAAS;;;KAjBhB", "debugId": null}}, {"offset": {"line": 9236, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/github/deer-flow/web/src/app/chat/main.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport { useMemo } from \"react\";\r\n\r\nimport { useStore } from \"~/core/store\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport { MessagesBlock } from \"./components/messages-block\";\r\nimport { ResearchBlock } from \"./components/research-block\";\r\n\r\nexport default function Main() {\r\n  const openResearchId = useStore((state) => state.openResearchId);\r\n  const doubleColumnMode = useMemo(\r\n    () => openResearchId !== null,\r\n    [openResearchId],\r\n  );\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex h-full w-full justify-center-safe px-4 pt-12 pb-4\",\r\n        doubleColumnMode && \"gap-8\",\r\n      )}\r\n    >\r\n      <MessagesBlock\r\n        className={cn(\r\n          \"shrink-0 transition-all duration-300 ease-out\",\r\n          !doubleColumnMode &&\r\n            `w-[768px] translate-x-[min(max(calc((100vw-538px)*0.75),575px)/2,960px/2)]`,\r\n          doubleColumnMode && `w-[538px]`,\r\n        )}\r\n      />\r\n      <ResearchBlock\r\n        className={cn(\r\n          \"w-[min(max(calc((100vw-538px)*0.75),575px),960px)] pb-4 transition-all duration-300 ease-out\",\r\n          !doubleColumnMode && \"scale-0\",\r\n          doubleColumnMode && \"\",\r\n        )}\r\n        researchId={openResearchId}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AAEA;AAAA;AACA;AAEA;AACA;;;AARA;;;;;;AAUe,SAAS;;IACtB,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;yCAAE,CAAC,QAAU,MAAM,cAAc;;IAC/D,MAAM,mBAAmB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;0CAC7B,IAAM,mBAAmB;yCACzB;QAAC;KAAe;IAElB,qBACE,sSAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA,oBAAoB;;0BAGtB,sSAAC,yJAAA,CAAA,gBAAa;gBACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,CAAC,oBACC,CAAC,0EAA0E,CAAC,EAC9E,oBAAoB,CAAC,SAAS,CAAC;;;;;;0BAGnC,sSAAC,yJAAA,CAAA,gBAAa;gBACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gGACA,CAAC,oBAAoB,WACrB,oBAAoB;gBAEtB,YAAY;;;;;;;;;;;;AAIpB;GA/BwB;;QACC,gIAAA,CAAA,WAAQ;;;KADT", "debugId": null}}]}