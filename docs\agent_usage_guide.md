# 智能体使用指南

DeerFlow 现在支持多个智能体，您可以通过在输入消息前添加 `@智能体名称` 来指定使用哪个智能体。

## 可用智能体

### 1. 默认智能体 (default_agent)
- **显示名称**: 默认智能体
- **功能**: 直接调用大模型回答问题，适合简单的对话和问答
- **使用场景**: 
  - 简单问答
  - 日常对话
  - 快速获取信息
- **使用方法**: `@默认智能体 你的问题`

### 2. 报告智能体 (reporter_agent) 
- **显示名称**: 报告智能体
- **功能**: 执行完整的研究工作流，生成详细的研究报告
- **使用场景**:
  - 深度研究
  - 生成详细报告
  - 需要多步骤分析的复杂任务
- **使用方法**: `@报告智能体 你的研究主题` 或直接输入（默认）

## 使用示例

### 使用默认智能体
```
@默认智能体 什么是人工智能？
```

### 使用报告智能体
```
@报告智能体 人工智能在医疗领域的应用现状和发展趋势
```

### 不指定智能体（默认使用报告智能体）
```
量子计算对密码学的影响
```

## 技术实现

### 后端实现
1. 在 `ChatRequest` 模型中添加了 `agent_name` 参数
2. 在 `/api/chat/stream` 接口中根据 `agent_name` 选择不同的处理逻辑
3. 新增 `_astream_default_agent_generator` 函数处理默认智能体的流式响应

### 前端实现
1. 在 `sendMessage` 函数中添加智能体检测逻辑
2. 解析以 `@` 开头的消息，提取智能体名称
3. 更新输入框提示信息，告知用户可用的智能体

## API 参数

### ChatRequest 新增参数
```python
agent_name: Optional[str] = Field(
    "reporter_agent", 
    description="The name of the agent to use (default_agent or reporter_agent)"
)
```

### 前端 chatStream 新增参数
```typescript
params: {
  // ... 其他参数
  agent_name?: string;
}
```

## 注意事项

1. 如果不指定智能体，系统默认使用报告智能体
2. 智能体名称必须完全匹配（区分大小写）
3. `@` 符号和智能体名称之间不能有空格
4. 智能体名称和实际问题之间必须有空格分隔

## 扩展新智能体

如需添加新的智能体，请按以下步骤操作：

1. 在后端 `chat_stream` 函数中添加新的条件分支
2. 实现对应的流式响应生成器函数
3. 在前端 `sendMessage` 函数中添加智能体名称映射
4. 更新输入框提示信息
5. 添加相应的测试用例 