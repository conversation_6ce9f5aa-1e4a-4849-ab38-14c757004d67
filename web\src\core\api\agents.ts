// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { resolveServiceURL } from "./resolve-service-url";

export interface Agent {
  name: string;
  display_name: string;
  description: string;
}

export interface AgentsResponse {
  agents: Agent[];
}

export async function getAgents(): Promise<Agent[]> {
  try {
    const response = await fetch(resolveServiceURL("agents"));
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data: AgentsResponse = await response.json();
    return data.agents;
  } catch (error) {
    console.error("Failed to fetch agents:", error);
    return [];
  }
} 