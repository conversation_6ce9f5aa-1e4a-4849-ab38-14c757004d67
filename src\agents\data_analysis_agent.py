# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
数据分析智能体适配器 - 将examples/data_analysis_agent_v2.py适配到DeerFlow
"""

import uuid
import os
import json
import re
import logging
import aiohttp
import httpx
import sqlparse
import time
from typing import Dict, List, Optional, AsyncGenerator
from dataclasses import dataclass

# 导入DeerFlow相关模块
from src.llms.llm import get_llm_by_type
from src.agents.utils import strip_agent_mention
from src.config.loader import load_yaml_config
from langchain_openai import ChatOpenAI
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class DataAnalysisInput:
    """数据分析智能体输入数据结构"""
    messages: List[dict]
    thread_id: str
    run_id: str

class TaskCancelledException(Exception):
    """任务取消异常"""
    pass

# 全局取消任务字典
cancelled_tasks = {}

def cancel_task(thread_id, run_id):
    """取消指定的任务"""
    cancelled_tasks[(thread_id, run_id)] = True
    logger.info(f"任务已标记为取消: thread_id={thread_id}, run_id={run_id}")

def clear_cancelled_task(thread_id, run_id):
    """清理已取消的任务标记"""
    cancelled_tasks.pop((thread_id, run_id), None)

class DataAnalysisAgent:
    """数据分析智能体 - DeerFlow适配版本"""
    
    # SQL生成相关的固定提示词
    SQL_GENERATION_RULES = """
## SQL生成规范
- **严格表关联规则**：JOIN条件必须在"表之间关系"中有明确定义，禁止创造不存在的关联关系
- **禁止随意关联**：如果两个表在"表之间关系"中没有直接关系，不要强行JOIN，考虑通过中间表关联
- **ID字段优先**：优先使用ID字段进行关联，避免使用名称字段进行字符串匹配
- **字段名称检查**：仔细检查字段名称，确保使用正确的字段进行关联
- 仅生成SELECT语句，禁止包含INSERT/UPDATE/DELETE等DML操作
- 严格嵌入提供的DDL元数据，禁止使用任何未声明的表或字段
- 表名带KMMOM schema
- 过滤删除数据
- 根据用户的语义及表结构，尽量对字段进行中文别名处理
- 严格检查使用的表字段，不要引用非涉及表的字段
"""

    SQL_OUTPUT_FORMAT = """
## 输出格式要求
- 只输出SQL语句，不要包含任何其他内容
- 不要输出"SQL:"、"生成的SQL:"等前缀
- 不要输出任何解释、注释或说明
- 不要输出markdown格式
- 不要输出代码块标记
- 直接输出纯SQL语句
"""

    def __init__(self):
        """初始化智能体"""
        # 获取DeerFlow配置文件路径
        config_path = str((Path(__file__).parent.parent.parent / "conf.yaml").resolve())
        config = load_yaml_config(config_path)
        
        # 初始化LLM实例，按配置顺序排列
        self.llm_instances = {}
        self.llm_retry_order = []
        
        # 按照BASIC_MODEL -> BASIC_MODEL1 -> BASIC_MODEL2的顺序初始化
        for model_key in ['BASIC_MODEL', 'BASIC_MODEL1', 'BASIC_MODEL2']:
            if model_key in config and config[model_key]:
                model_config = config[model_key]
                try:
                    # 使用ChatOpenAI创建LLM实例
                    llm_instance = ChatOpenAI(
                        model=model_config.get("model", "gpt-3.5-turbo"),
                        api_key=model_config.get("api_key"),
                        base_url=model_config.get("base_url"),
                        temperature=0
                    )
                    self.llm_instances[model_key.lower()] = llm_instance
                    self.llm_retry_order.append(model_key.lower())
                    logger.info(f"初始化LLM成功: {model_key} -> {model_config.get('model', 'unknown')}")
                except Exception as e:
                    logger.warning(f"初始化LLM失败: {model_key}, 错误: {e}")
        
        # 如果没有配置任何模型，使用默认配置
        if not self.llm_instances:
            logger.warning("未找到有效的LLM配置，使用默认BASIC_MODEL")
            self.llm = get_llm_by_type("basic")
            self.llm_instances = {'basic': self.llm}
            self.llm_retry_order = ['basic']
        else:
            # 使用第一个配置的模型作为默认
            first_model_key = self.llm_retry_order[0]
            self.llm = self.llm_instances[first_model_key]
        
        logger.info(f"LLM重试顺序: {' -> '.join(self.llm_retry_order)}")
        
        # 从环境变量读取top_k值，默认为40
        self.default_top_k = int(os.getenv("TOP_K", "40"))
        
        # 缓存表结构和关系信息，按thread_id存储
        self.db_structure_cache = {}  # {thread_id: db_structure}
        self.relationships_cache = {}  # {thread_id: relationships}
    
    def _get_cached_db_info(self, thread_id: str) -> tuple[str, list]:
        """获取缓存的数据库信息"""
        db_structure = self.db_structure_cache.get(thread_id, "")
        relationships = self.relationships_cache.get(thread_id, [])
        return db_structure, relationships
    
    def _cache_db_info(self, thread_id: str, db_structure: str, relationships: list):
        """缓存数据库信息"""
        self.db_structure_cache[thread_id] = db_structure
        self.relationships_cache[thread_id] = relationships
    
    def _clear_cache(self, thread_id: str):
        """清理指定线程的缓存"""
        self.db_structure_cache.pop(thread_id, None)
        self.relationships_cache.pop(thread_id, None)
    
    async def _recognize_intent(self, messages: List) -> str:
        """意图识别 - 简化版本"""
        logger.info(f"开始意图识别，消息数: {len(messages)}")
        
        if not messages:
            return "initial"
        
        # 获取最后一条用户消息
        last_message = messages[-1]
        if not isinstance(last_message, dict) or last_message.get("role") != "user":
            return "initial"
        
        content = strip_agent_mention(last_message.get("content", ""))
        logger.info(f"分析用户消息: {content}")
        
        # 使用大模型进行意图识别
        return await self._llm_intent_recognition(messages)
    
    async def _llm_intent_recognition(self, messages: List) -> str:
        """使用大模型进行意图识别"""
        # 构建对话历史
        conversation_history = ""
        has_sql_generated = False
        last_user_message = ""
        
        for msg in messages:
            if isinstance(msg, dict):
                if msg.get("role") == "user":
                    user_content = strip_agent_mention(msg.get("content", ""))
                    conversation_history += f"用户: {user_content}\n"
                    last_user_message = user_content
                elif msg.get("role") == "assistant":
                    content_preview = msg.get("content", "")[:200] + "..." if len(msg.get("content", "")) > 200 else msg.get("content", "")
                    conversation_history += f"助手: {content_preview}\n"
                    # 检查是否已经生成过SQL
                    if "SQL生成完毕" in msg.get("content", "") or "```sql" in msg.get("content", ""):
                        has_sql_generated = True
        
        # 构建意图识别提示
        intent_prompt = f"""你是一个数据分析意图识别助手。请分析用户的最新消息，判断用户的意图。

对话历史：
{conversation_history}

用户最新消息：{last_user_message}

请判断用户的最新消息属于以下哪种意图：
1. "initial" - 用户想要进行新的数据分析（首次查询，包含分析需求）
2. "new_query" - 用户想要进行全新的数据分析（明确提到新的查询需求，与之前的分析无关）
3. "modify_sql" - 用户想要修改之前生成的SQL（对当前SQL提出修改意见或优化建议）

判断规则：
- 如果对话中还没有生成过SQL，且用户提出分析需求，选择 "initial"
- 如果已经生成过SQL，但用户提出了**完全不同的数据分析需求**，选择 "new_query"
- 如果已经生成过SQL，用户提出修改意见（如"改一下"、"优化"、"加个条件"等），选择 "modify_sql"

当前是否已生成SQL: {"是" if has_sql_generated else "否"}

请只回复意图类型，不要包含其他内容：initial、new_query 或 modify_sql"""

        try:
            # 调用大模型（带重试机制）
            messages_for_llm = [{"role": "user", "content": intent_prompt}]
            
            intent_response = await self._call_llm_with_retry(messages_for_llm)
            # 从AIMessage对象中提取content
            intent_result = intent_response.content if hasattr(intent_response, 'content') else str(intent_response)
            intent_result = intent_result.strip().lower()
            
            # 验证返回的意图
            if intent_result in ["initial", "new_query", "modify_sql"]:
                logger.info(f"大模型识别意图: {intent_result}")
                return intent_result
            else:
                logger.warning(f"大模型返回了无效的意图: {intent_result}，默认使用 initial")
                return "initial"
                
        except Exception as e:
            logger.error(f"大模型意图识别失败: {e}，默认使用 initial")
            return "initial"
    
    def _extract_user_query(self, messages: List) -> str:
        """提取用户查询"""
        for msg in reversed(messages):
            if isinstance(msg, dict) and msg.get("role") == "user":
                content = strip_agent_mention(msg.get("content", ""))
                return content.strip()
        return ""
    
    def _extract_modification_request(self, messages: List) -> str:
        """提取修改请求"""
        for msg in reversed(messages):
            if isinstance(msg, dict) and msg.get("role") == "user":
                content = strip_agent_mention(msg.get("content", ""))
                return content.strip()
        return ""
    
    def _extract_previous_sql(self, messages: List) -> str:
        """从对话历史中提取之前生成的SQL"""
        for msg in reversed(messages):
            if isinstance(msg, dict) and msg.get("role") == "assistant":
                content = msg.get("content", "")
                # 查找SQL代码块
                sql_match = re.search(r'```sql\n(.*?)\n```', content, re.DOTALL)
                if sql_match:
                    return sql_match.group(1).strip()
        return ""
    
    async def _extract_keywords(self, user_input: str, thread_id: str = "", run_id: str = "") -> str:
        """提取关键词"""
        if thread_id and run_id:
            self._check_cancelled_and_raise(thread_id, run_id)
            
        prompt = f"""请从以下查询中提取关键词用于数据库搜索：

查询内容：{user_input}

提取要求：
1. 优先提取业务实体（如：工作中心、生产订单、物料、人员等）
2. 提取业务属性（如：创建者、负责人、状态、类型等）
3. 忽略具体的人名、日期、数值等实例数据
4. 输出格式：关键词之间用空格分隔
5. 按重要性排序：业务实体 > 业务属性 > 其他相关词汇
6. 只返回关键词，不要其他解释

请提取关键词："""
        
        messages = [
            {"role": "system", "content": "你是一个专业的数据库查询关键词提取助手，专注于提取业务实体和属性。"},
            {"role": "user", "content": prompt}
        ]
        keywords_result = await self._call_llm_with_retry(messages)
        return keywords_result
    
    async def _fetch_db_structure(self, keywords: str, thread_id: str = "", run_id: str = "", top_k: int = None) -> str:
        """获取数据库结构"""
        if thread_id and run_id:
            self._check_cancelled_and_raise(thread_id, run_id)
        
        if top_k is None:
            top_k = self.default_top_k
            
        db_structure_url = os.getenv("DB_STRUCTURE_SEARCH_URL", "http://**************:28000/search")
        
        async with aiohttp.ClientSession() as session:
            url = f"{db_structure_url}?query={keywords}&top_k={top_k}"
            async with session.get(url) as resp:
                if resp.status == 200:
                    return await resp.text()
                else:
                    raise Exception(f"数据库结构接口请求失败: {resp.status}")
    
    async def _fetch_relationships(self, keywords: str, thread_id: str = "", run_id: str = "") -> list:
        """获取表关系"""
        if thread_id and run_id:
            self._check_cancelled_and_raise(thread_id, run_id)
            
        NEO4J_URI = os.getenv("NEO4J_URI", "bolt://**************:7687")
        NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
        NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "neo4j123")
        
        try:
            from neo4j import GraphDatabase
            driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
            try:
                with driver.session() as session:
                    query = """
                    MATCH (a:Table)-[r:REFERENCES]->(b:Table)
                    RETURN a.name AS source_table, r.from AS source_field,
                           b.name AS target_table, r.to AS target_field
                    """
                    result = session.run(query)
                    relationships = []
                    for record in result:
                        rel = f"{record['source_table']}({record['source_field']}) references {record['target_table']}({record['target_field']})"
                        relationships.append(rel)
                return relationships
            finally:
                driver.close()
        except ImportError:
            logger.warning("Neo4j driver not available, returning empty relationships")
            return []
        except Exception as e:
            logger.error(f"获取表关系失败: {e}")
            return []
    
    def _build_sql_generation_prompt(self, user_query: str, modification_request: str = "", 
                                   db_structure: str = "", relationships: List[str] = None,
                                   previous_sql: str = "", is_retry: bool = False) -> str:
        """构建SQL生成提示词"""
        prompt = f"""
你是一名专业的达梦SQL生成助手。请根据以下信息生成SQL：

用户需求: {user_query}
"""
        if modification_request:
            prompt += f"\n用户修改意见: {modification_request}"
        if previous_sql and not is_retry:
            prompt += f"\n上次SQL:\n{previous_sql}"
        
        prompt += f"""

表结构格式: <表名>[表注释](<字段名1>:<类型>:<注释>:[枚举值1:注释1,枚举值2:注释2,...], ...)
表之间关系格式：表A元数据(字段名) REFERENCES 表B元数据(字段名)

表结构:
{db_structure}

表之间关系:
{relationships if relationships else '无'}

{self.SQL_GENERATION_RULES}

{self.SQL_OUTPUT_FORMAT}

请根据上述信息生成SQL。
"""
        
        return prompt
    
    async def _execute_sql(self, sql: str) -> Dict:
        """执行SQL"""
        sql_exec_url = os.getenv("SQL_EXEC_URL", "http://**************:18086/api/sql/execute/v2")
        
        async with httpx.AsyncClient(timeout=30) as client:
            response = await client.post(sql_exec_url, json={"sql": sql, "limit": 20})
            response.raise_for_status()
            return response.json()
    
    def _generate_table_json(self, data: List[Dict]) -> Dict:
        """生成结构化表格JSON"""
        if not data or len(data) == 0:
            return {
                "type": "table",
                "data": [],
                "total": 0
            }
        return {
            "type": "table",
            "data": data,
            "total": len(data)
        }

    def _extract_sql_from_response(self, response: str) -> str:
        """从AI响应中提取SQL语句"""
        import re

        # 尝试提取SQL代码块
        sql_pattern = r'```sql\s*(.*?)\s*```'
        match = re.search(sql_pattern, response, re.DOTALL | re.IGNORECASE)
        if match:
            return match.group(1).strip()

        # 尝试提取SELECT语句
        select_pattern = r'(SELECT\s+.*?(?:;|$))'
        match = re.search(select_pattern, response, re.DOTALL | re.IGNORECASE)
        if match:
            return match.group(1).strip()

        # 如果都没找到，返回原始响应
        return response.strip()
    
    def _is_cancelled(self, thread_id: str, run_id: str) -> bool:
        """检查任务是否被取消"""
        is_cancelled = cancelled_tasks.get((thread_id, run_id), False)
        if is_cancelled:
            logger.info(f"检测到任务已取消: thread_id={thread_id}, run_id={run_id}")
        return is_cancelled
    
    def _check_cancelled_and_raise(self, thread_id: str, run_id: str):
        """检查取消状态，如果已取消则抛出异常"""
        if self._is_cancelled(thread_id, run_id):
            raise TaskCancelledException("任务已取消")
    
    def _generate_dynamic_system_prompt(self, attempt: int = 0) -> str:
        """生成动态的system提示词，避免缓存"""
        base_prompts = [
            "你是一个专业的达梦SQL生成助手。",
            "作为经验丰富的达梦数据库专家，请协助生成SQL查询。", 
            "现在需要你扮演资深的达梦SQL开发工程师角色。",
            "请以精通达梦数据库的SQL专家身份来处理这个任务。",
            "担任专业的达梦数据库分析师，协助完成SQL生成工作。",
        ]
        
        prompt_index = attempt % len(base_prompts)
        return base_prompts[prompt_index]
    
    async def _call_llm_with_retry(self, messages: List[dict], max_retries: int = None) -> str:
        """带重试机制的LLM调用"""
        if max_retries is None:
            max_retries = len(self.llm_retry_order)
        
        last_error = None
        
        for attempt in range(max_retries):
            if attempt >= len(self.llm_retry_order):
                break
                
            model_key = self.llm_retry_order[attempt]
            llm_instance = self.llm_instances[model_key]
            
            try:
                logger.info(f"尝试使用LLM: {model_key} (第{attempt + 1}次尝试)")
                result = await llm_instance.ainvoke(messages)
                logger.info(f"LLM调用成功: {model_key}")
                return result.content if hasattr(result, 'content') else str(result)
            except Exception as e:
                last_error = e
                logger.warning(f"LLM调用失败: {model_key}, 错误: {e}")
                if attempt < len(self.llm_retry_order) - 1:
                    logger.info(f"将尝试下一个LLM: {self.llm_retry_order[attempt + 1]}")
                continue
        
        # 所有重试都失败了
        raise Exception(f"所有LLM调用都失败了，最后一个错误: {last_error}")
    
    async def _stream_llm_with_retry(self, messages: List[dict], max_retries: int = None) -> AsyncGenerator[str, None]:
        """带重试机制的LLM流式调用"""
        if max_retries is None:
            max_retries = len(self.llm_retry_order)
        
        last_error = None
        
        for attempt in range(max_retries):
            if attempt >= len(self.llm_retry_order):
                break
                
            model_key = self.llm_retry_order[attempt]
            llm_instance = self.llm_instances[model_key]
            
            try:
                logger.info(f"尝试流式调用LLM: {model_key} (第{attempt + 1}次尝试)")
                async for chunk in llm_instance.astream(messages):
                    yield chunk
                logger.info(f"LLM流式调用成功: {model_key}")
                return
            except Exception as e:
                last_error = e
                logger.warning(f"LLM流式调用失败: {model_key}, 错误: {e}")
                if attempt < len(self.llm_retry_order) - 1:
                    logger.info(f"将尝试下一个LLM: {self.llm_retry_order[attempt + 1]}")
                continue
        
        # 所有重试都失败了
        raise Exception(f"所有LLM流式调用都失败了，最后一个错误: {last_error}")
    
    async def run(self, input_data: DataAnalysisInput) -> AsyncGenerator[str, None]:
        """运行智能体的主方法 - 生成SSE格式的流式输出"""
        logger.info(f"数据分析Agent开始执行")
        
        thread_id = input_data.thread_id
        run_id = input_data.run_id
        message_id = str(uuid.uuid4())
        
        try:
            # 1. 意图识别
            intent = await self._recognize_intent(input_data.messages)
            logger.info(f"识别意图: {intent}")
            
            user_query = self._extract_user_query(input_data.messages)
            
            # 发送开始事件
            yield f"event: run_started\ndata: {json.dumps({'thread_id': thread_id, 'run_id': run_id}, ensure_ascii=False)}\n\n"
            yield f"event: message_start\ndata: {json.dumps({'message_id': message_id, 'role': 'assistant'}, ensure_ascii=False)}\n\n"
            
            if intent == "initial":
                yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': f'[日志] 开始分析查询：「{user_query}」\\n'}, ensure_ascii=False)}\n\n"
            elif intent == "new_query":
                yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': f'[日志] 开始新的数据分析：「{user_query}」\\n'}, ensure_ascii=False)}\n\n"
            elif intent == "modify_sql":
                yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[日志] 收到SQL修改请求，正在重新生成并执行...\\n'}, ensure_ascii=False)}\n\n"
            
            # 检查取消状态
            if self._is_cancelled(thread_id, run_id):
                yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[日志] 任务已取消\\n'}, ensure_ascii=False)}\n\n"
                return
            
            # 如果需要获取基础数据
            if intent in ["initial", "new_query"]:
                if intent == "new_query":
                    self._clear_cache(thread_id)

                # 1. 关键词提取步骤
                yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[STEP_START] keyword_extraction\\n'}, ensure_ascii=False)}\n\n"
                yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '**步骤1: 关键词提取**\\n\\n正在分析用户查询，提取关键词...\\n\\n'}, ensure_ascii=False)}\n\n"

                keywords = await self._extract_keywords(user_query, thread_id, run_id)

                yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': f'✅ **提取结果:**\\n- 关键词: `{keywords}`\\n\\n'}, ensure_ascii=False)}\n\n"
                yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[STEP_END] keyword_extraction\\n'}, ensure_ascii=False)}\n\n"

                if self._is_cancelled(thread_id, run_id):
                    return

                # 2. 数据库结构分析步骤
                yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[STEP_START] schema_analysis\\n'}, ensure_ascii=False)}\n\n"
                yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '**步骤2: 数据库结构分析**\\n\\n正在获取相关数据库表结构...\\n\\n'}, ensure_ascii=False)}\n\n"

                db_structure, relationships = self._get_cached_db_info(thread_id)
                if not db_structure or not relationships:
                    db_structure = await self._fetch_db_structure(keywords, thread_id, run_id)

                    # 显示获取到的表结构
                    table_count = len(db_structure.split('CREATE TABLE')) - 1 if db_structure else 0
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': f'✅ **获取结果:**\\n- 找到 {table_count} 个相关数据表\\n- 表结构信息已获取\\n\\n'}, ensure_ascii=False)}\n\n"

                    if self._is_cancelled(thread_id, run_id):
                        return

                    # 3. 表关系分析
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '正在分析表之间的关系...\\n\\n'}, ensure_ascii=False)}\n\n"

                    relationships = await self._fetch_relationships(keywords, thread_id, run_id)

                    rel_count = len(relationships) if isinstance(relationships, list) else 0
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': f'✅ **关系分析结果:**\\n- 发现 {rel_count} 个表关系\\n- 关系信息已获取\\n\\n'}, ensure_ascii=False)}\n\n"

                    # 缓存获取的信息
                    self._cache_db_info(thread_id, db_structure, relationships)
                else:
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '✅ **使用缓存数据:**\\n- 数据库结构信息 (已缓存)\\n- 表关系信息 (已缓存)\\n\\n'}, ensure_ascii=False)}\n\n"

                yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[STEP_END] schema_analysis\\n'}, ensure_ascii=False)}\n\n"
            else:
                # 对于修改请求，使用缓存的结构信息
                db_structure, relationships = self._get_cached_db_info(thread_id)
            
            if self._is_cancelled(thread_id, run_id):
                return

            # 3. SQL生成步骤
            yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[STEP_START] sql_generation\\n'}, ensure_ascii=False)}\n\n"
            yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '**步骤3: SQL查询生成**\\n\\n正在基于用户需求和数据库结构生成SQL查询...\\n\\n'}, ensure_ascii=False)}\n\n"

            # 构建提示词
            modification_request = self._extract_modification_request(input_data.messages) if intent == "modify_sql" else ""
            previous_sql = self._extract_previous_sql(input_data.messages) if intent == "modify_sql" else ""

            prompt = self._build_sql_generation_prompt(
                user_query=user_query,
                modification_request=modification_request,
                db_structure=db_structure,
                relationships=relationships,
                previous_sql=previous_sql
            )

            # 生成SQL（带重试机制）
            system_prompt = self._generate_dynamic_system_prompt(0)

            sql = ""
            async for chunk in self._stream_llm_with_retry([
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ]):
                self._check_cancelled_and_raise(thread_id, run_id)
                chunk_content = chunk.content if hasattr(chunk, 'content') else str(chunk)
                sql += chunk_content

            sql = sql.strip()
            formatted_sql = sqlparse.format(sql, reindent=True, keyword_case='upper')
            logger.info(f"生成SQL=\n{sql}")

            yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '✅ **生成的SQL查询:**\\n\\n'}, ensure_ascii=False)}\n\n"
            yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': f'```sql\\n{formatted_sql}\\n```\\n\\n'}, ensure_ascii=False)}\n\n"
            yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[STEP_END] sql_generation\\n'}, ensure_ascii=False)}\n\n"
            
            # 4. SQL执行步骤
            yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[STEP_START] sql_execution\\n'}, ensure_ascii=False)}\n\n"
            yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '**步骤4: SQL执行**\\n\\n正在执行SQL查询...\\n\\n'}, ensure_ascii=False)}\n\n"

            if self._is_cancelled(thread_id, run_id):
                yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '❌ 任务已取消\\n'}, ensure_ascii=False)}\n\n"
                return

            try:
                # 执行SQL
                result = await self._execute_sql(sql)
                logger.info(f"SQL执行结果: {result}")

                # 检查结果
                if isinstance(result, dict) and result.get('total', 0) > 0:
                    # 执行成功
                    data = result.get("data", [])
                    total = result.get("total", len(data) if isinstance(data, list) else 0)

                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': f'✅ **执行成功!**\\n- 查询返回: {total} 条记录\\n- 执行时间: < 1秒\\n\\n'}, ensure_ascii=False)}\n\n"
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[STEP_END] sql_execution\\n'}, ensure_ascii=False)}\n\n"

                    # 5. 结果展示步骤
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[STEP_START] result_display\\n'}, ensure_ascii=False)}\n\n"
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '**步骤5: 结果展示**\\n\\n'}, ensure_ascii=False)}\n\n"

                    # 生成表格JSON
                    table_json = self._generate_table_json(data if isinstance(data, list) else [])
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': f'[TABLE_DATA] {json.dumps(table_json, ensure_ascii=False)}\\n'}, ensure_ascii=False)}\n\n"

                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': f'✅ **数据展示完成**\\n- 共展示 {min(total, 20)} 条记录\\n- 总计 {total} 条数据\\n\\n'}, ensure_ascii=False)}\n\n"
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[STEP_END] result_display\\n'}, ensure_ascii=False)}\n\n"
                else:
                    # 执行失败，尝试重新生成SQL
                    error_msg = result.get('msg', '查询结果为空或执行失败') if isinstance(result, dict) else '执行失败'
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': f'❌ **首次执行失败:** {error_msg}\\n\\n'}, ensure_ascii=False)}\n\n"
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[STEP_END] sql_execution\\n'}, ensure_ascii=False)}\n\n"

                    # 6. SQL重新生成步骤
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[STEP_START] sql_regeneration\\n'}, ensure_ascii=False)}\n\n"
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '**步骤6: SQL重新生成**\\n\\n正在基于错误信息重新生成SQL...\\n\\n'}, ensure_ascii=False)}\n\n"

                    # 这里可以添加重新生成SQL的逻辑
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': f'⚠️ **需要手动调整:** {error_msg}\\n\\n'}, ensure_ascii=False)}\n\n"
                    yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[STEP_END] sql_regeneration\\n'}, ensure_ascii=False)}\n\n"

            except Exception as e:
                error_msg = str(e)
                yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': f'❌ **执行异常:** {error_msg}\\n\\n'}, ensure_ascii=False)}\n\n"
                yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[STEP_END] sql_execution\\n'}, ensure_ascii=False)}\n\n"
            
        except TaskCancelledException as e:
            logger.info(f"任务被取消: {str(e)}")
            yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': '[日志] 任务已取消\\n'}, ensure_ascii=False)}\n\n"
        except Exception as e:
            logger.error(f"捕获到异常: {type(e).__name__}: {str(e)}")
            yield f"event: message_content\ndata: {json.dumps({'message_id': message_id, 'delta': f'[错误] 执行过程中发生错误: {str(e)}\\n'}, ensure_ascii=False)}\n\n"
        
        # 发送结束事件
        yield f"event: message_end\ndata: {json.dumps({'message_id': message_id}, ensure_ascii=False)}\n\n"
        yield f"event: run_finished\ndata: {json.dumps({'thread_id': thread_id, 'run_id': run_id}, ensure_ascii=False)}\n\n"
        
        # 清理取消状态
        clear_cancelled_task(thread_id, run_id)