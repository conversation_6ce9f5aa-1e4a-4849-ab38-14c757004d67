# 数据分析智能体设置说明

## 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# 数据分析智能体配置
DB_STRUCTURE_SEARCH_URL=http://**************:28000/search
SQL_EXEC_URL=http://**************:18086/api/sql/execute/v2
NEO4J_URI=bolt://**************:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=neo4j123
TOP_K=40
```

## 依赖安装

确保安装以下Python依赖：

```bash
pip install aiohttp httpx sqlparse neo4j
```

## 使用方法

1. 启动DeerFlow服务
2. 在聊天界面中使用 `@数据分析智能体` 来调用
3. 输入自然语言查询，如："查询所有用户的订单数量"

## 功能特性

- ✅ 自然语言转SQL
- ✅ 智能缓存机制
- ✅ 多LLM支持（按conf.yaml配置：BASIC_MODEL → BASIC_MODEL1 → BASIC_MODEL2）
- ✅ 流式输出
- ✅ 实时任务取消
- ✅ 表格数据展示
- ✅ 自动重试机制（LLM调用失败时自动切换到下一个配置的模型）

## 注意事项

- 需要确保外部服务（数据库结构搜索、SQL执行、Neo4j）可访问
- 首次使用时会自动获取并缓存数据库结构信息
- 支持会话级别的缓存，提高后续查询效率
- LLM重试顺序完全基于`conf.yaml`中的配置，如果只配置了BASIC_MODEL，则只使用该模型
- 建议配置多个LLM以提高系统稳定性和容错能力 