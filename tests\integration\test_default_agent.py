# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import pytest
from unittest.mock import AsyncMock, patch
from src.server.app import _astream_default_agent_generator
from src.server.chat_request import ChatRequest


class TestDefaultAgent:
    """测试默认智能体功能"""

    @pytest.mark.asyncio
    async def test_default_agent_generator(self):
        """测试默认智能体生成器"""
        messages = [
            {"role": "user", "content": "你好，请介绍一下自己"}
        ]
        thread_id = "test_thread"
        
        # Mock LLM response
        mock_chunk = AsyncMock()
        mock_chunk.id = "test_id"
        mock_chunk.content = "你好！我是默认智能体，可以直接回答您的问题。"
        mock_chunk.response_metadata = {"finish_reason": "stop"}
        
        with patch('src.server.app.get_llm_by_type') as mock_get_llm:
            mock_llm = AsyncMock()
            mock_llm.astream.return_value = [mock_chunk]
            mock_get_llm.return_value = mock_llm
            
            # 收集生成器的输出
            results = []
            async for event in _astream_default_agent_generator(messages, thread_id):
                results.append(event)
            
            # 验证结果
            assert len(results) == 1
            assert "default_agent" in results[0]
            assert "你好！我是默认智能体" in results[0]

    def test_chat_request_with_agent_name(self):
        """测试ChatRequest包含agent_name参数"""
        request_data = {
            "messages": [{"role": "user", "content": "测试消息"}],
            "agent_name": "default_agent"
        }
        
        request = ChatRequest(**request_data)
        assert request.agent_name == "default_agent"
        
        # 测试默认值
        request_default = ChatRequest(messages=[{"role": "user", "content": "测试"}])
        assert request_default.agent_name == "reporter_agent"

    def test_agent_name_detection(self):
        """测试智能体名称检测逻辑"""
        # 这里模拟前端的智能体检测逻辑
        def detect_agent(content: str):
            if content and content.startswith("@"):
                space_index = content.find(" ")
                if space_index > 0:
                    mentioned_agent = content[1:space_index]
                    actual_content = content[space_index + 1:].strip()
                    
                    if mentioned_agent == "默认智能体":
                        return "default_agent", actual_content
                    elif mentioned_agent == "报告智能体":
                        return "reporter_agent", actual_content
            return "reporter_agent", content
        
        # 测试默认智能体
        agent, content = detect_agent("@默认智能体 你好")
        assert agent == "default_agent"
        assert content == "你好"
        
        # 测试报告智能体
        agent, content = detect_agent("@报告智能体 生成报告")
        assert agent == "reporter_agent"
        assert content == "生成报告"
        
        # 测试无@符号
        agent, content = detect_agent("普通消息")
        assert agent == "reporter_agent"
        assert content == "普通消息" 