# 数据分析智能体使用指南

## 快速开始

### 1. 调用数据分析智能体

在聊天界面中输入：
```
@数据分析智能体 查询所有用户的订单数量
```

或者直接选择数据分析智能体后输入查询：
```
查询最近一周的销售数据
```

### 2. 支持的查询类型

- **统计查询**: "查询用户总数"、"统计订单数量"
- **条件查询**: "查询北京地区的用户"、"查询金额大于1000的订单"
- **时间范围查询**: "查询最近一周的数据"、"查询2024年的销售记录"
- **聚合查询**: "按地区统计用户数量"、"计算平均订单金额"

## 界面功能说明

### 1. 智能体状态指示

- **蓝色脉冲**: 正在分析中
- **绿色对勾**: 分析完成
- **红色警告**: 执行出错

### 2. 分析流程展示

数据分析智能体会展示完整的分析流程：

1. **关键词提取**: 从您的查询中提取关键信息
2. **数据库结构分析**: 获取相关的数据库表结构
3. **SQL查询生成**: 基于您的需求生成SQL语句
4. **查询执行**: 执行SQL并获取结果
5. **结果展示**: 以表格形式展示查询结果

### 3. 交互功能

- **展开/折叠步骤**: 点击步骤卡片查看详细信息
- **复制SQL**: 点击复制按钮获取生成的SQL语句
- **切换视图**: 在表格视图和原始数据视图之间切换

### 4. 数据展示

- **表格视图**: 格式化的表格展示，便于阅读
- **原始数据**: JSON格式的原始数据，便于开发调试
- **分页显示**: 自动限制显示条数，避免界面过载

## 最佳实践

### 1. 查询建议

- 使用清晰、具体的描述
- 包含必要的条件和范围
- 避免过于复杂的嵌套查询

### 2. 错误处理

如果查询失败，智能体会：
- 显示具体的错误信息
- 提供可能的解决建议
- 保留查询历史便于调试

### 3. 性能优化

- 大数据量查询会自动限制返回条数
- 复杂查询会显示执行进度
- 支持查询中断和重试

## 示例查询

### 基础查询
```
查询用户表中的所有记录
```

### 条件查询
```
查询状态为活跃的用户
```

### 统计查询
```
统计每个城市的用户数量
```

### 时间范围查询
```
查询最近30天的订单数据
```

## 注意事项

1. **数据安全**: 智能体只能查询，不能修改数据
2. **查询限制**: 单次查询最多返回20条记录
3. **超时处理**: 复杂查询可能需要较长时间，请耐心等待
4. **错误重试**: 如果查询失败，可以尝试重新表述查询需求

## 技术说明

### 支持的数据库
- 当前支持关系型数据库查询
- 支持标准SQL语法
- 支持复杂的JOIN和聚合操作

### 安全机制
- 自动SQL注入防护
- 只读权限控制
- 查询结果数量限制

### 性能特性
- 智能缓存机制
- 查询优化建议
- 实时执行监控
